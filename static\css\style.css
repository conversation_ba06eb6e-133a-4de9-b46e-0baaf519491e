* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: #141414;
    color: white;
    line-height: 1.6;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: linear-gradient(180deg, rgba(0,0,0,0.7) 10%, transparent);
    z-index: 1000;
    padding: 20px 0;
    transition: background-color 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.nav-logo h1 {
    color: #e50914;
    font-size: 1.8rem;
    font-weight: bold;
}

.nav-menu {
    display: flex;
    gap: 30px;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-size: 1rem;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #e50914;
}

/* Hero Section */
.hero-section {
    height: 100vh;
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), 
                url('https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.hero-content {
    max-width: 600px;
    padding: 0 20px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 30px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
}

.hero-button {
    background-color: #e50914;
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.2rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.hero-button:hover {
    background-color: #f40612;
}

/* Movies Section */
.movies-section {
    padding: 80px 0;
    background-color: #141414;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-title {
    font-size: 2rem;
    margin-bottom: 30px;
    color: white;
}

.movies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.movie-card {
    position: relative;
    background-color: #222;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.movie-card:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
}

.movie-poster {
    width: 100%;
    height: 350px;
    object-fit: cover;
}

.movie-info {
    padding: 15px;
}

.movie-title {
    font-size: 1.2rem;
    margin-bottom: 5px;
    color: white;
}

.movie-year, .movie-genre {
    color: #999;
    font-size: 0.9rem;
}

.movie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.movie-card:hover .movie-overlay {
    opacity: 1;
}

.play-button, .info-button {
    background-color: #e50914;
    color: white;
    border: none;
    padding: 10px 20px;
    margin: 5px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.play-button:hover, .info-button:hover {
    background-color: #f40612;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
}

.modal-content {
    background-color: #222;
    margin: 5% auto;
    padding: 20px;
    border-radius: 10px;
    width: 80%;
    max-width: 600px;
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: white;
}

/* Watch Page */
.watch-container {
    padding-top: 80px;
    min-height: 100vh;
}

.video-player {
    background-color: #000;
    text-align: center;
}

.movie-details {
    padding: 40px 0;
}

.movie-meta {
    margin: 20px 0;
}

.movie-meta span {
    margin-right: 20px;
    padding: 5px 10px;
    background-color: #333;
    border-radius: 3px;
    font-size: 0.9rem;
}

.movie-description {
    font-size: 1.1rem;
    line-height: 1.8;
    margin: 20px 0;
    color: #ccc;
}

.action-buttons {
    margin-top: 30px;
}

.btn-primary, .btn-secondary {
    padding: 12px 24px;
    margin-right: 15px;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #e50914;
    color: white;
}

.btn-primary:hover {
    background-color: #f40612;
}

.btn-secondary {
    background-color: #333;
    color: white;
}

.btn-secondary:hover {
    background-color: #555;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .nav-menu {
        display: none;
    }
}
