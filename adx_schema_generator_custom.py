#
# Python Version: 3.13 or higher
#
# Dependencies: This script requires 'requests' and 'beautifulsoup4'.
# To install them, run the following command in your terminal:
# pip install requests beautifulsoup4
#
# ---
#
# Usage:
# By default, this script processes a predefined list of 10 Defender XDR tables
# and outputs all commands to a single text file.
#
#   # To run for the default list of 10 tables:
#   python adx_schema_generator.py
#
#   # To process a custom list of tables instead:
#   python adx_schema_generator.py -t EmailEvents DeviceProcessEvents
#
#   # To change the output file name:
#   python adx_schema_generator.py -o my_commands.kql
#
# ---

import requests
from bs4 import BeautifulSoup
import argparse
import sys
import time

# --- Configuration ---
BASE_URL_TEMPLATE = "https://learn.microsoft.com/en-us/defender-xdr/advanced-hunting-{table_name_lower}-table"

# The default list of tables to process if none are provided via the command line.
DEFAULT_TABLE_LIST = [
    "CloudAppEvents",
    "EmailAttachmentInfo",
    "EmailEvents",
    "EmailPostDeliveryEvents",
    "EmailUrlInfo",
    "IdentityDirectoryEvents",
    "IdentityInfo",
    "IdentityLogonEvents",
    "IdentityQueryEvents",
    "UrlClickEvents"
]

WEB_TO_ADX_TYPE_MAP = {
    'boolean': 'bool',
    'bool': 'bool',
    'string': 'string',
    'datetime': 'datetime',
    'long': 'long',
    'dynamic': 'dynamic',
    'int': 'int'
}

ADX_TYPE_TO_KQL_CAST_MAP = {
    'bool': 'tobool',
    'string': 'tostring',
    'datetime': 'todatetime',
    'long': 'tolong',
    'dynamic': 'todynamic',
    'int': 'toint'
}

def scrape_schema_from_url(url: str) -> list[tuple[str, str]]:
    """Scrapes a Microsoft Learn page to find the table schema."""
    print(f"[*] Scraping schema from: {url}")
    try:
        response = requests.get(url, timeout=15)
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        if hasattr(e, 'response') and e.response is not None and e.response.status_code == 404:
             print(f"[!] Error: 404 Not Found. The table page may not exist or follow the standard URL pattern.", file=sys.stderr)
        else:
            print(f"[!] Error: Could not fetch the URL. {e}", file=sys.stderr)
        return []
    
    soup = BeautifulSoup(response.text, 'html.parser')
    table = soup.select_one('main#main table')
    if not table:
        print("[!] Error: Could not find a schema table on the page.", file=sys.stderr)
        return []

    schema = []
    for row in table.find('tbody').find_all('tr'):
        cells = row.find_all('td')
        if len(cells) >= 2:
            schema.append((cells[0].get_text(strip=True), cells[1].get_text(strip=True).lower()))
    
    if schema:
        print(f"[*] Successfully extracted {len(schema)} columns.")
    else:
        print("[!] Warning: Found a table but could not extract schema.", file=sys.stderr)
        
    return schema

def generate_create_table_command(schema: list, table_name: str) -> str:
    """Generates the .create table KQL command."""
    columns_definitions = [f"{col_name}:{WEB_TO_ADX_TYPE_MAP.get(web_type, 'string')}" for col_name, web_type in schema]
    return f".create table {table_name} ({', '.join(columns_definitions)})"

def generate_function_command(schema: list, table_name: str) -> str:
    """Generates the .create-or-alter function for the update policy."""
    projections = [f"{col_name} = {ADX_TYPE_TO_KQL_CAST_MAP.get(WEB_TO_ADX_TYPE_MAP.get(web_type, 'string'), 'tostring')}(events.properties.{col_name})" for col_name, web_type in schema]
    kql_body = f"""{table_name}Raw
| mv-expand events = records
| project {',\n    '.join(projections)}"""
    return f".create-or-alter function {table_name}Expand {{\n{kql_body}\n}}"

def generate_update_policy_command(table_name: str) -> str:
    """Generates the .alter table policy update command."""
    policy_json = f'[{{"Source": "{table_name}Raw", "Query": "{table_name}Expand()", "IsEnabled": "True", "IsTransactional": "true"}}]'
    return f".alter table {table_name} policy update @'{policy_json}'"

def main():
    """Main function to parse arguments and run the script."""
    parser = argparse.ArgumentParser(
        description="Generate ADX KQL commands for multiple Microsoft Defender XDR schema pages.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        '-t', '--tables',
        nargs='*',
        default=DEFAULT_TABLE_LIST,
        help='A space-separated list of table names to process.\nIf not provided, processes the default list of 10 tables.'
    )
    parser.add_argument(
        '-o', '--output-file',
        default='generated_kql_commands.txt',
        help='The name of the file to save the generated KQL commands.'
    )
    args = parser.parse_args()

    all_kql_commands = []
    total_tables = len(args.tables)
    print(f"[*] Starting batch process for {total_tables} tables.")

    for i, table_name in enumerate(args.tables, 1):
        print(f"\n--- Processing table {i}/{total_tables}: {table_name} ---")
        
        url = BASE_URL_TEMPLATE.format(table_name_lower=table_name.lower())
        schema = scrape_schema_from_url(url)
        
        if schema:
            # Generate the 3 commands
            cmd_create_table = generate_create_table_command(schema, table_name)
            cmd_create_function = generate_function_command(schema, table_name)
            cmd_update_policy = generate_update_policy_command(table_name)
            
            # Add commands to the collection with KQL-style comments for readability
            all_kql_commands.append(f"-- =====================================================================")
            all_kql_commands.append(f"-- Commands for table: {table_name}")
            all_kql_commands.append(f"-- =====================================================================")
            all_kql_commands.append("\n-- 1. Command to Create Destination Table")
            all_kql_commands.append(cmd_create_table + "\n")
            all_kql_commands.append("-- 2. Command to Create Transformation Function")
            all_kql_commands.append(cmd_create_function + "\n")
            all_kql_commands.append("-- 3. Command to Set the Update Policy on the Table")
            all_kql_commands.append(cmd_update_policy + "\n")
        else:
            print(f"[!] Skipping command generation for {table_name} due to scraping failure.")
        
        # Add a small delay to be respectful to the server
        time.sleep(1)

    if all_kql_commands:
        print("\n--- Batch processing complete ---")
        try:
            with open(args.output_file, 'w') as f:
                f.write('\n'.join(all_kql_commands))
            print(f"[*] Successfully wrote all commands to file: '{args.output_file}'")
        except IOError as e:
            print(f"[!] Error: Could not write to file '{args.output_file}'. {e}", file=sys.stderr)
    else:
        print("\n[!] No commands were generated in this run.")

if __name__ == '__main__':
    main()