import pyautogui

import time

from datetime import datetime

 

# --- Configuration ---

# Time (in seconds) between scroll lock key presses

INTERVAL_SECONDS = 60  # 60 seconds = 1 minute

# --- End Configuration ---

 

print("--- Keep Active Script ---")

print(f"Pressing Scroll Lock every {INTERVAL_SECONDS} seconds.")

print("Press Ctrl+C in this window to stop the script.")

print("-" * 28)

 

# Keep failsafe option (move mouse to top-left corner to stop script)

pyautogui.FAILSAFE = True

 

try:

    while True:

        try:

            now_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

 

            # Press Scroll Lock key

            pyautogui.press('scrolllock')

 

            print(f"{now_str}: Pressed Scroll Lock. Waiting {INTERVAL_SECONDS}s...")

 

            # Wait for the specified interval

            time.sleep(INTERVAL_SECONDS)

 

        except pyautogui.FailSafeException:

            print("\nFail-safe triggered! Mouse moved to a corner.")

            print("<PERSON><PERSON><PERSON> stopped by fail-safe.")

            break

        except Exception as e:

            # Catch other potential errors during the loop

            print(f"\nAn error occurred: {e}")

            print("Pausing for 15 seconds before trying again...")

            time.sleep(15)

 

except KeyboardInterrupt:

    print("\nCtrl+C detected. Script stopped by user.")

except Exception as e:

    # Catch errors during initial setup or unexpected exit

    print(f"\nAn unexpected error stopped the script: {e}")

 

finally:

    print("--- Script Finished ---")