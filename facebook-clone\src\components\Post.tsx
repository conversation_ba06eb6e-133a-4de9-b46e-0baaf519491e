'use client'

import { Heart, MessageCircle, Share, MoreHorizontal } from 'lucide-react'

interface Comment {
  id: number
  user: {
    name: string
    avatar: string
  }
  content: string
  timestamp: string
}

interface PostData {
  id: number
  user: {
    name: string
    avatar: string
  }
  content: string
  image?: string
  timestamp: string
  likes: number
  comments: number
  shares: number
  isLiked: boolean
  commentList?: Comment[]
}

interface PostProps {
  post: PostData
  onLike: (postId: number) => void
}

import { useState } from 'react'
import CommentSection from './CommentSection'

export default function Post({ post, onLike }: PostProps) {
  const [showComments, setShowComments] = useState(false)
  const [commentList, setCommentList] = useState<Comment[]>(
    post.commentList || [
      {
        id: 1,
        user: {
          name: '<PERSON>',
          avatar: 'https://via.placeholder.com/40/ff6b6b/ffffff?text=JS'
        },
        content: 'Great post! Thanks for sharing.',
        timestamp: '5 min ago'
      },
      {
        id: 2,
        user: {
          name: '<PERSON>',
          avatar: 'https://via.placeholder.com/40/4ecdc4/ffffff?text=MJ'
        },
        content: 'I totally agree with this!',
        timestamp: '20 min ago'
      }
    ]
  )

  const handleAddComment = (postId: number, content: string) => {
    const newComment: Comment = {
      id: commentList.length + 1,
      user: {
        name: 'John Doe',
        avatar: 'https://via.placeholder.com/40/1877f2/ffffff?text=JD'
      },
      content,
      timestamp: 'Just now'
    }
    
    setCommentList([...commentList, newComment])
  }

  return (
    <div className="post-card">
      {/* Post Header */}
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-3">
          <img
            src={post.user.avatar}
            alt={post.user.name}
            className="w-10 h-10 rounded-full"
          />
          <div>
            <div className="font-semibold text-facebook-text-dark">
              {post.user.name}
            </div>
            <div className="text-sm text-facebook-text-gray">
              {post.timestamp}
            </div>
          </div>
        </div>
        <button className="p-2 hover:bg-gray-100 rounded-full">
          <MoreHorizontal className="w-5 h-5 text-facebook-text-gray" />
        </button>
      </div>

      {/* Post Content */}
      <div className="px-4 pb-3">
        <p className="text-facebook-text-dark leading-relaxed">
          {post.content}
        </p>
      </div>

      {/* Post Image */}
      {post.image && (
        <div className="mb-3">
          <img
            src={post.image}
            alt="Post content"
            className="w-full h-auto max-h-96 object-cover"
          />
        </div>
      )}

      {/* Post Stats */}
      <div className="px-4 py-2 flex items-center justify-between text-sm text-facebook-text-gray border-b border-gray-200">
        <div className="flex items-center space-x-1">
          {post.likes > 0 && (
            <>
              <div className="w-5 h-5 bg-facebook-blue rounded-full flex items-center justify-center">
                <Heart className="w-3 h-3 text-white fill-current" />
              </div>
              <span>{post.likes}</span>
            </>
          )}
        </div>
        <div className="flex space-x-4">
          {commentList.length > 0 && (
            <button 
              onClick={() => setShowComments(!showComments)}
              className="hover:underline"
            >
              {commentList.length} comments
            </button>
          )}
          {post.shares > 0 && <span>{post.shares} shares</span>}
        </div>
      </div>

      {/* Post Actions */}
      <div className="flex items-center justify-around p-2">
        <button
          onClick={() => onLike(post.id)}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors flex-1 justify-center ${
            post.isLiked ? 'text-facebook-blue' : 'text-facebook-text-gray'
          }`}
        >
          <Heart className={`w-5 h-5 ${post.isLiked ? 'fill-current' : ''}`} />
          <span className="font-medium">Like</span>
        </button>
        
        <button 
          onClick={() => setShowComments(!showComments)}
          className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors flex-1 justify-center text-facebook-text-gray"
        >
          <MessageCircle className="w-5 h-5" />
          <span className="font-medium">Comment</span>
        </button>
        
        <button className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors flex-1 justify-center text-facebook-text-gray">
          <Share className="w-5 h-5" />
          <span className="font-medium">Share</span>
        </button>
      </div>

      {/* Comments Section */}
      {showComments && (
        <CommentSection 
          postId={post.id}
          comments={commentList}
          currentUser={{
            name: 'John Doe',
            avatar: 'https://via.placeholder.com/40/1877f2/ffffff?text=JD'
          }}
          onAddComment={handleAddComment}
        />
      )}
    </div>
  )
}
