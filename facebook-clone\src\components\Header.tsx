'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Search, Home, Users, MessageCircle, Bell, Menu, LogOut } from 'lucide-react'
import Notifications from './Notifications'

interface User {
  id: number
  name: string
  avatar: string
  email: string
}

interface HeaderProps {
  user: User
}

export default function Header({ user }: HeaderProps) {
  const router = useRouter()
  const [showUserMenu, setShowUserMenu] = useState(false)

  const handleLogout = () => {
    localStorage.removeItem('facebookUser')
    router.push('/login')
  }

  return (
    <header className="fixed top-0 left-0 right-0 bg-white shadow-md z-50 border-b border-gray-200">
      <div className="flex items-center justify-between px-4 py-2 max-w-7xl mx-auto">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          <Link href="/" className="text-2xl font-bold text-facebook-blue">
            facebook
          </Link>
          <div className="hidden md:flex relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search Facebook"
              className="pl-10 pr-4 py-2 bg-facebook-gray rounded-full w-64 focus:outline-none focus:ring-2 focus:ring-facebook-blue"
            />
          </div>
        </div>

        {/* Center Navigation */}
        <div className="hidden md:flex items-center space-x-2">
          <Link href="/" className="p-3 rounded-lg hover:bg-gray-100 text-facebook-blue">
            <Home className="w-6 h-6" />
          </Link>
          <button className="p-3 rounded-lg hover:bg-gray-100 text-gray-600">
            <Users className="w-6 h-6" />
          </button>
          <button className="p-3 rounded-lg hover:bg-gray-100 text-gray-600">
            <MessageCircle className="w-6 h-6" />
          </button>
          <Notifications />
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-3">
          <button className="md:hidden p-2 rounded-lg hover:bg-gray-100">
            <Menu className="w-6 h-6" />
          </button>
          
          <div className="relative">
            <div 
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-2 cursor-pointer hover:bg-gray-100 rounded-lg p-2"
            >
              <img
                src={user.avatar}
                alt={user.name}
                className="w-8 h-8 rounded-full"
              />
              <span className="hidden md:block font-semibold text-facebook-text-dark">
                {user.name}
              </span>
            </div>
            
            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-60 bg-white rounded-lg shadow-lg z-50">
                <div className="p-2">
                  <Link 
                    href="/profile" 
                    className="flex items-center space-x-2 p-3 hover:bg-gray-100 rounded-lg"
                    onClick={() => setShowUserMenu(false)}
                  >
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="w-10 h-10 rounded-full"
                    />
                    <div>
                      <div className="font-semibold">{user.name}</div>
                      <div className="text-xs text-facebook-text-gray">See your profile</div>
                    </div>
                  </Link>
                </div>
                
                <div className="border-t border-gray-200 p-2">
                  <button 
                    onClick={handleLogout}
                    className="flex items-center space-x-2 w-full p-3 hover:bg-gray-100 rounded-lg text-left"
                  >
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <LogOut className="w-4 h-4" />
                    </div>
                    <span className="font-medium">Log Out</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
