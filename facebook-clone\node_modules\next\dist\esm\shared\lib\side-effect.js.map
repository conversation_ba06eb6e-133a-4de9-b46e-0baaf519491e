{"version": 3, "sources": ["../../../src/shared/lib/side-effect.tsx"], "names": ["Children", "useEffect", "useLayoutEffect", "isServer", "window", "useClientOnlyLayoutEffect", "useClientOnlyEffect", "SideEffect", "props", "headManager", "reduceComponentsToState", "emitChange", "mountedInstances", "headElements", "toArray", "Array", "from", "filter", "Boolean", "updateHead", "add", "children", "delete", "_pendingUpdate"], "mappings": "AACA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,eAAe,QAAQ,QAAO;AAe5D,MAAMC,WAAW,OAAOC,WAAW;AACnC,MAAMC,4BAA4BF,WAAW,KAAO,IAAID;AACxD,MAAMI,sBAAsBH,WAAW,KAAO,IAAIF;AAElD,eAAe,SAASM,WAAWC,KAAsB;IACvD,MAAM,EAAEC,WAAW,EAAEC,uBAAuB,EAAE,GAAGF;IAEjD,SAASG;QACP,IAAIF,eAAeA,YAAYG,gBAAgB,EAAE;YAC/C,MAAMC,eAAeb,SAASc,OAAO,CACnCC,MAAMC,IAAI,CAACP,YAAYG,gBAAgB,EAA0BK,MAAM,CACrEC;YAGJT,YAAYU,UAAU,CAACT,wBAAwBG,cAAcL;QAC/D;IACF;IAEA,IAAIL,UAAU;YACZM;QAAAA,gCAAAA,gCAAAA,YAAaG,gBAAgB,qBAA7BH,8BAA+BW,GAAG,CAACZ,MAAMa,QAAQ;QACjDV;IACF;IAEAN,0BAA0B;YACxBI;QAAAA,gCAAAA,gCAAAA,YAAaG,gBAAgB,qBAA7BH,8BAA+BW,GAAG,CAACZ,MAAMa,QAAQ;QACjD,OAAO;gBACLZ;YAAAA,gCAAAA,gCAAAA,YAAaG,gBAAgB,qBAA7BH,8BAA+Ba,MAAM,CAACd,MAAMa,QAAQ;QACtD;IACF;IAEA,kFAAkF;IAClF,oFAAoF;IACpF,gEAAgE;IAChE,qFAAqF;IACrF,mFAAmF;IACnFhB,0BAA0B;QACxB,IAAII,aAAa;YACfA,YAAYc,cAAc,GAAGZ;QAC/B;QACA,OAAO;YACL,IAAIF,aAAa;gBACfA,YAAYc,cAAc,GAAGZ;YAC/B;QACF;IACF;IAEAL,oBAAoB;QAClB,IAAIG,eAAeA,YAAYc,cAAc,EAAE;YAC7Cd,YAAYc,cAAc;YAC1Bd,YAAYc,cAAc,GAAG;QAC/B;QACA,OAAO;YACL,IAAId,eAAeA,YAAYc,cAAc,EAAE;gBAC7Cd,YAAYc,cAAc;gBAC1Bd,YAAYc,cAAc,GAAG;YAC/B;QACF;IACF;IAEA,OAAO;AACT"}