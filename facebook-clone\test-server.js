console.log('Starting Facebook Clone server...');

const http = require('http');

const server = http.createServer((req, res) => {
  console.log(`Request received: ${req.method} ${req.url}`);
  
  res.writeHead(200, { 'Content-Type': 'text/html' });
  res.end(`
    <html>
      <head>
        <title>Facebook Clone - Test</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          .header { background: #1877f2; color: white; padding: 20px; border-radius: 8px; }
          .content { margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🎉 Facebook Clone is Working!</h1>
        </div>
        <div class="content">
          <h2>Server Status: ✅ Running</h2>
          <p>Your Node.js Facebook clone server is successfully running!</p>
          <p>Time: ${new Date().toLocaleString()}</p>
        </div>
      </body>
    </html>
  `);
});

const PORT = 3000;

server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Facebook Clone server is running!`);
  console.log(`📱 Open your browser and go to: http://localhost:${PORT}`);
  console.log('🛑 Press Ctrl+C to stop the server');
});

server.on('error', (err) => {
  console.error('Server error:', err);
});

console.log('Server setup complete!');
