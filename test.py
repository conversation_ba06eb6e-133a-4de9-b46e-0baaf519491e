def check_abbreviation_match(pattern: str, text: str) -> bool:
    """
    Checks if a given input string matches an "i18n-style" pattern.

    Args:
        pattern: The pattern string, which can contain letters and numbers.
                 A number N means N characters are skipped.
                 e.g., "i18n", "f6k", "F2eb2k", "8".
        text: The input string to check against the pattern.
                 e.g., "internationalization", "facebook".

    Returns:
        True if the input string matches the pattern, False otherwise.
    """
    p_len = len(pattern)
    s_len = len(text)
    p_ptr = 0  # Pointer for the pattern
    s_ptr = 0  # Pointer for the text string

    while p_ptr < p_len:
        char_p = pattern[p_ptr]

        if char_p.isalpha():
            if s_ptr >= s_len:
                return False
            if char_p.lower() != text[s_ptr].lower():
                return False
            p_ptr += 1
            s_ptr += 1
        elif char_p.isdigit():
            num_str = ""
            while p_ptr < p_len and pattern[p_ptr].isdigit():
                num_str += pattern[p_ptr]
                p_ptr += 1
            if not num_str: 
                return False 
            skip_count = int(num_str)
            if s_ptr + skip_count > s_len:
                return False
            s_ptr += skip_count
        else:
            return False 

    return p_ptr == p_len and s_ptr == s_len

pattern = "i18n"
text = "internationalization"
result = check_abbreviation_match(pattern, text)
print(f"Pattern: '{pattern}', Text: '{text}' -> Matches: {result}")