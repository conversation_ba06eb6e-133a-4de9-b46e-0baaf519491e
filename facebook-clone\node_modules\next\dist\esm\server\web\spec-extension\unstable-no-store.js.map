{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-no-store.ts"], "names": ["staticGenerationAsyncStorage", "markCurrentScopeAsDynamic", "unstable_noStore", "callingExpression", "store", "getStore", "forceStatic", "isUnstableNoStore"], "mappings": "AAAA,SAASA,4BAA4B,QAAQ,sEAAqE;AAClH,SAASC,yBAAyB,QAAQ,qCAAoC;AAE9E;;;;;;;;;;;;;;CAcC,GACD,OAAO,SAASC;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,QAAQJ,6BAA6BK,QAAQ;IACnD,IAAI,CAACD,OAAO;QACV,6FAA6F;QAC7F,6FAA6F;QAC7F,uEAAuE;QACvE;IACF,OAAO,IAAIA,MAAME,WAAW,EAAE;QAC5B;IACF,OAAO;QACLF,MAAMG,iBAAiB,GAAG;QAC1BN,0BAA0BG,OAAOD;IACnC;AACF"}