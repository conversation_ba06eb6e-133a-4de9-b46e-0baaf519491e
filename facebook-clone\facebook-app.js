const http = require('http');

console.log('🔧 Starting Facebook Clone server...');

const server = http.createServer((req, res) => {
  console.log(`📥 ${req.method} ${req.url}`);
  
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
  
  const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Clone</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        .facebook-blue { background-color: #1877f2; }
        .facebook-blue-hover:hover { background-color: #166fe5; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 bg-white shadow-md z-50 border-b border-gray-200">
        <div class="flex items-center justify-between px-4 py-2 max-w-6xl mx-auto">
            <div class="flex items-center space-x-4">
                <h1 class="text-2xl font-bold text-blue-600">facebook</h1>
                <div class="hidden md:flex relative">
                    <input type="text" placeholder="Search Facebook" 
                           class="pl-10 pr-4 py-2 bg-gray-100 rounded-full w-64 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
            
            <div class="hidden md:flex items-center space-x-2">
                <button class="p-3 rounded-lg hover:bg-gray-100 text-blue-600">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                </button>
                <button class="p-3 rounded-lg hover:bg-gray-100 text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </button>
            </div>
            
            <div class="flex items-center space-x-3">
                <div class="flex items-center space-x-2 cursor-pointer hover:bg-gray-100 rounded-lg p-2">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">JD</div>
                    <span class="hidden md:block font-semibold text-gray-800">John Doe</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="pt-16 max-w-2xl mx-auto p-4">
        <!-- Create Post -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
            <div class="flex space-x-3">
                <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">JD</div>
                <button class="flex-1 text-left px-4 py-3 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors text-gray-500">
                    What's on your mind, John?
                </button>
            </div>
            <div class="flex items-center justify-between mt-3 pt-3 border-t border-gray-200">
                <button class="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <span class="text-gray-600 font-medium">Photo/video</span>
                </button>
                <button class="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-gray-600 font-medium">Feeling/activity</span>
                </button>
            </div>
        </div>

        <!-- Posts -->
        <div class="space-y-4">
            <!-- Post 1 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between p-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center text-white font-bold">JS</div>
                        <div>
                            <div class="font-semibold text-gray-800">Jane Smith</div>
                            <div class="text-sm text-gray-500">2 hours ago</div>
                        </div>
                    </div>
                </div>
                <div class="px-4 pb-3">
                    <p class="text-gray-800 leading-relaxed">
                        Just finished building an amazing React app! 🚀 The feeling when everything clicks together is incredible. #coding #react #webdev
                    </p>
                </div>
                <div class="mb-3">
                    <img src="https://images.unsplash.com/photo-1633356122544-f134324a6cee?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80" 
                         alt="Coding" class="w-full h-auto max-h-96 object-cover">
                </div>
                <div class="px-4 py-2 flex items-center justify-between text-sm text-gray-500 border-b border-gray-200">
                    <div class="flex items-center space-x-1">
                        <div class="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                            </svg>
                        </div>
                        <span>24</span>
                    </div>
                    <div class="flex space-x-4">
                        <span>8 comments</span>
                        <span>3 shares</span>
                    </div>
                </div>
                <div class="flex items-center justify-around p-2">
                    <button class="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors flex-1 justify-center text-gray-500">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        <span class="font-medium">Like</span>
                    </button>
                    <button class="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors flex-1 justify-center text-gray-500">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        <span class="font-medium">Comment</span>
                    </button>
                    <button class="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors flex-1 justify-center text-gray-500">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                        </svg>
                        <span class="font-medium">Share</span>
                    </button>
                </div>
            </div>

            <!-- Post 2 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center justify-between p-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">MJ</div>
                        <div>
                            <div class="font-semibold text-gray-800">Mike Johnson</div>
                            <div class="text-sm text-gray-500">4 hours ago</div>
                        </div>
                    </div>
                </div>
                <div class="px-4 pb-3">
                    <p class="text-gray-800 leading-relaxed">
                        Beautiful sunset from my balcony today. Sometimes you need to pause and appreciate the simple things in life. 🌅
                    </p>
                </div>
                <div class="mb-3">
                    <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80" 
                         alt="Sunset" class="w-full h-auto max-h-96 object-cover">
                </div>
                <div class="px-4 py-2 flex items-center justify-between text-sm text-gray-500 border-b border-gray-200">
                    <div class="flex items-center space-x-1">
                        <div class="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                            </svg>
                        </div>
                        <span>156</span>
                    </div>
                    <div class="flex space-x-4">
                        <span>23 comments</span>
                        <span>12 shares</span>
                    </div>
                </div>
                <div class="flex items-center justify-around p-2">
                    <button class="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors flex-1 justify-center text-blue-600">
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 24 24">
                            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                        </svg>
                        <span class="font-medium">Like</span>
                    </button>
                    <button class="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors flex-1 justify-center text-gray-500">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        <span class="font-medium">Comment</span>
                    </button>
                    <button class="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors flex-1 justify-center text-gray-500">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                        </svg>
                        <span class="font-medium">Share</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('🎉 Facebook Clone loaded successfully!');
        
        // Add some interactivity
        document.querySelectorAll('button').forEach(button => {
            if (button.textContent.includes('Like')) {
                button.addEventListener('click', function() {
                    const isLiked = this.classList.contains('text-blue-600');
                    if (isLiked) {
                        this.classList.remove('text-blue-600');
                        this.classList.add('text-gray-500');
                    } else {
                        this.classList.remove('text-gray-500');
                        this.classList.add('text-blue-600');
                    }
                });
            }
        });
    </script>
</body>
</html>`;
  
  res.end(html);
});

const PORT = 3000;
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Facebook Clone server running at http://localhost:${PORT}`);
  console.log('📱 Open your browser and visit the URL above');
  console.log('🛑 Press Ctrl+C to stop the server');
});

server.on('error', (err) => {
  console.error('❌ Server error:', err);
});
