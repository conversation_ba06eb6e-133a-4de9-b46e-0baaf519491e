{% extends "base.html" %}

{% block title %}{{ movie.title }} - Netflix Clone{% endblock %}

{% block content %}
<div class="watch-container">
    <div class="video-player">
        <video controls autoplay width="100%" height="500">
            <source src="{{ movie.video_url }}" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    </div>
    
    <div class="movie-details">
        <div class="container">
            <h1 class="movie-title">{{ movie.title }}</h1>
            <div class="movie-meta">
                <span class="movie-year">{{ movie.year }}</span>
                <span class="movie-rating">{{ movie.rating }}</span>
                <span class="movie-genre">{{ movie.genre }}</span>
            </div>
            <p class="movie-description">{{ movie.description }}</p>
            
            <div class="action-buttons">
                <button class="btn-primary" onclick="togglePlay()">⏸ Pause</button>
                <button class="btn-secondary" onclick="window.history.back()">← Back to Browse</button>
                <button class="btn-secondary">+ My List</button>
            </div>
        </div>
    </div>
</div>

<script>
function togglePlay() {
    const video = document.querySelector('video');
    const button = document.querySelector('.btn-primary');
    
    if (video.paused) {
        video.play();
        button.textContent = '⏸ Pause';
    } else {
        video.pause();
        button.textContent = '▶ Play';
    }
}
</script>
{% endblock %}
