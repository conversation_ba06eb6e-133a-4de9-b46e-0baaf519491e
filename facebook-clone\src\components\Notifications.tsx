'use client'

import { useState } from 'react'
import { <PERSON>, Check, <PERSON> } from 'lucide-react'

interface Notification {
  id: number
  user: {
    name: string
    avatar: string
  }
  content: string
  timestamp: string
  isRead: boolean
  type: 'like' | 'comment' | 'friend' | 'tag' | 'birthday'
}

export default function Notifications() {
  const [isOpen, setIsOpen] = useState(false)
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: 1,
      user: {
        name: '<PERSON>',
        avatar: 'https://via.placeholder.com/40/ff6b6b/ffffff?text=JS'
      },
      content: 'liked your post',
      timestamp: '5 min ago',
      isRead: false,
      type: 'like'
    },
    {
      id: 2,
      user: {
        name: '<PERSON>',
        avatar: 'https://via.placeholder.com/40/4ecdc4/ffffff?text=MJ'
      },
      content: 'commented on your photo',
      timestamp: '2 hours ago',
      isRead: false,
      type: 'comment'
    },
    {
      id: 3,
      user: {
        name: '<PERSON>',
        avatar: 'https://via.placeholder.com/40/45b7d1/ffffff?text=SW'
      },
      content: 'sent you a friend request',
      timestamp: '1 day ago',
      isRead: true,
      type: 'friend'
    },
    {
      id: 4,
      user: {
        name: '<PERSON> Chen',
        avatar: 'https://via.placeholder.com/40/f39c12/ffffff?text=AC'
      },
      content: 'tagged you in a post',
      timestamp: '2 days ago',
      isRead: true,
      type: 'tag'
    },
    {
      id: 5,
      user: {
        name: 'Emma Thompson',
        avatar: 'https://via.placeholder.com/40/e74c3c/ffffff?text=ET'
      },
      content: 'has a birthday today',
      timestamp: 'Today',
      isRead: false,
      type: 'birthday'
    }
  ])

  const unreadCount = notifications.filter(notification => !notification.isRead).length

  const markAsRead = (id: number) => {
    setNotifications(notifications.map(notification => 
      notification.id === id ? { ...notification, isRead: true } : notification
    ))
  }

  const markAllAsRead = () => {
    setNotifications(notifications.map(notification => ({ ...notification, isRead: true })))
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'like':
        return <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center text-red-500">❤️</div>
      case 'comment':
        return <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-500">💬</div>
      case 'friend':
        return <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-green-500">👥</div>
      case 'tag':
        return <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-500">🏷️</div>
      case 'birthday':
        return <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center text-yellow-500">🎂</div>
      default:
        return <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-gray-500">📢</div>
    }
  }

  return (
    <div className="relative">
      <button 
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 rounded-full hover:bg-gray-100"
      >
        <Bell className="w-6 h-6 text-gray-600" />
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
            {unreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold">Notifications</h3>
              {unreadCount > 0 && (
                <button 
                  onClick={markAllAsRead}
                  className="text-facebook-blue text-sm hover:underline"
                >
                  Mark all as read
                </button>
              )}
            </div>
          </div>

          <div className="divide-y divide-gray-100">
            {notifications.length === 0 ? (
              <div className="p-4 text-center text-facebook-text-gray">
                No notifications
              </div>
            ) : (
              notifications.map(notification => (
                <div 
                  key={notification.id} 
                  className={`p-3 flex items-start hover:bg-gray-50 ${!notification.isRead ? 'bg-blue-50' : ''}`}
                >
                  <div className="mr-3">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="flex items-center space-x-2">
                          <img
                            src={notification.user.avatar}
                            alt={notification.user.name}
                            className="w-10 h-10 rounded-full"
                          />
                          <div>
                            <div className="font-medium">
                              <span className="font-semibold">{notification.user.name}</span> {notification.content}
                            </div>
                            <div className="text-xs text-facebook-text-gray">
                              {notification.timestamp}
                            </div>
                          </div>
                        </div>
                      </div>
                      {!notification.isRead && (
                        <button 
                          onClick={() => markAsRead(notification.id)}
                          className="p-1 hover:bg-gray-200 rounded-full"
                        >
                          <Check className="w-4 h-4 text-facebook-text-gray" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          <div className="p-3 text-center border-t border-gray-200">
            <button className="text-facebook-blue hover:underline">
              See all notifications
            </button>
          </div>
        </div>
      )}
    </div>
  )
}