// Smooth scrolling to movies section
function scrollToMovies() {
    document.getElementById('movies').scrollIntoView({
        behavior: 'smooth'
    });
}

// Movie modal functionality
function openMovieModal(movieId) {
    fetch(`/api/movies/${movieId}`)
        .then(response => response.json())
        .then(movie => {
            const modalBody = document.getElementById('modalBody');
            modalBody.innerHTML = `
                <h2>${movie.title}</h2>
                <div class="modal-movie-info">
                    <img src="${movie.thumbnail}" alt="${movie.title}" style="width: 200px; float: left; margin-right: 20px; border-radius: 5px;">
                    <div>
                        <p><strong>Year:</strong> ${movie.year}</p>
                        <p><strong>Genre:</strong> ${movie.genre}</p>
                        <p><strong>Rating:</strong> ${movie.rating}</p>
                        <p><strong>Description:</strong> ${movie.description}</p>
                        <div style="margin-top: 20px;">
                            <button class="btn-primary" onclick="watchMovie(${movie.id})">▶ Watch Now</button>
                            <button class="btn-secondary">+ Add to List</button>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('movieModal').style.display = 'block';
        })
        .catch(error => {
            console.error('Error fetching movie details:', error);
        });
}

function closeMovieModal() {
    document.getElementById('movieModal').style.display = 'none';
}

function watchMovie(movieId) {
    window.location.href = `/watch/${movieId}`;
}

// Close modal when clicking outside of it
window.onclick = function(event) {
    const modal = document.getElementById('movieModal');
    if (event.target === modal) {
        closeMovieModal();
    }
}

// Navbar scroll effect
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 100) {
        navbar.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
    } else {
        navbar.style.backgroundColor = 'transparent';
    }
});

// Search functionality (basic)
function searchMovies(query) {
    fetch('/api/movies')
        .then(response => response.json())
        .then(movies => {
            const filteredMovies = movies.filter(movie => 
                movie.title.toLowerCase().includes(query.toLowerCase()) ||
                movie.genre.toLowerCase().includes(query.toLowerCase())
            );
            displayMovies(filteredMovies);
        });
}

function displayMovies(movies) {
    const moviesGrid = document.querySelector('.movies-grid');
    moviesGrid.innerHTML = '';
    
    movies.forEach(movie => {
        const movieCard = document.createElement('div');
        movieCard.className = 'movie-card';
        movieCard.onclick = () => openMovieModal(movie.id);
        
        movieCard.innerHTML = `
            <img src="${movie.thumbnail}" alt="${movie.title}" class="movie-poster">
            <div class="movie-info">
                <h3 class="movie-title">${movie.title}</h3>
                <p class="movie-year">${movie.year}</p>
                <p class="movie-genre">${movie.genre}</p>
            </div>
            <div class="movie-overlay">
                <button class="play-button" onclick="event.stopPropagation(); watchMovie(${movie.id})">
                    ▶ Play
                </button>
                <button class="info-button" onclick="event.stopPropagation(); openMovieModal(${movie.id})">
                    ℹ More Info
                </button>
            </div>
        `;
        
        moviesGrid.appendChild(movieCard);
    });
}
