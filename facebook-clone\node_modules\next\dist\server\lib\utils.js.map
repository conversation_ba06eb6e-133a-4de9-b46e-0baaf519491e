{"version": 3, "sources": ["../../../src/server/lib/utils.ts"], "names": ["RESTART_EXIT_CODE", "checkNodeDebugType", "getDebugPort", "getMaxOldSpaceSize", "getNodeOptionsWithoutInspect", "myParseInt", "printAndExit", "message", "code", "console", "log", "error", "process", "exit", "debugPortStr", "execArgv", "find", "localArg", "startsWith", "split", "env", "NODE_OPTIONS", "match", "parseInt", "NODE_INSPECT_RE", "replace", "value", "parsedValue", "isNaN", "isFinite", "InvalidArgumentError", "nodeDebugType", "undefined", "some", "maxOldSpaceSize"], "mappings": ";;;;;;;;;;;;;;;;;;;;IAwCaA,iBAAiB;eAAjBA;;IAEGC,kBAAkB;eAAlBA;;IA9BHC,YAAY;eAAZA;;IAkDGC,kBAAkB;eAAlBA;;IApCAC,4BAA4B;eAA5BA;;IAIAC,UAAU;eAAVA;;IA5BAC,YAAY;eAAZA;;;2BAFqB;AAE9B,SAASA,aAAaC,OAAe,EAAEC,OAAO,CAAC;IACpD,IAAIA,SAAS,GAAG;QACdC,QAAQC,GAAG,CAACH;IACd,OAAO;QACLE,QAAQE,KAAK,CAACJ;IAChB;IAEAK,QAAQC,IAAI,CAACL;AACf;AAEO,MAAMN,eAAe;QAExBU,wBAOAA,iCAAAA,kCAAAA;IARF,MAAME,eACJF,EAAAA,yBAAAA,QAAQG,QAAQ,CACbC,IAAI,CACH,CAACC,WACCA,SAASC,UAAU,CAAC,gBACpBD,SAASC,UAAU,CAAC,sCAJ1BN,uBAMIO,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,OACpBP,4BAAAA,QAAQQ,GAAG,CAACC,YAAY,sBAAxBT,mCAAAA,0BAA0BU,KAAK,sBAA/BV,kCAAAA,sCAAAA,2BAAkC,sDAAlCA,+BAAqE,CAAC,EAAE;IAC1E,OAAOE,eAAeS,SAAST,cAAc,MAAM;AACrD;AAEA,MAAMU,kBAAkB;AACjB,SAASpB;IACd,OAAO,AAACQ,CAAAA,QAAQQ,GAAG,CAACC,YAAY,IAAI,EAAC,EAAGI,OAAO,CAACD,iBAAiB;AACnE;AAEO,SAASnB,WAAWqB,KAAa;IACtC,sCAAsC;IACtC,MAAMC,cAAcJ,SAASG,OAAO;IAEpC,IAAIE,MAAMD,gBAAgB,CAACE,SAASF,gBAAgBA,cAAc,GAAG;QACnE,MAAM,IAAIG,+BAAoB,CAAC,CAAC,CAAC,EAAEJ,MAAM,+BAA+B,CAAC;IAC3E;IACA,OAAOC;AACT;AAEO,MAAM3B,oBAAoB;AAE1B,SAASC;QAKZW,iCAAAA,2BAOAA,kCAAAA;IAXF,IAAImB,gBAAgBC;IAEpB,IACEpB,QAAQG,QAAQ,CAACkB,IAAI,CAAC,CAAChB,WAAaA,SAASC,UAAU,CAAC,mBACxDN,4BAAAA,QAAQQ,GAAG,CAACC,YAAY,sBAAxBT,kCAAAA,0BAA0BU,KAAK,qBAA/BV,qCAAAA,2BAAkC,2BAClC;QACAmB,gBAAgB;IAClB;IAEA,IACEnB,QAAQG,QAAQ,CAACkB,IAAI,CAAC,CAAChB,WAAaA,SAASC,UAAU,CAAC,uBACxDN,6BAAAA,QAAQQ,GAAG,CAACC,YAAY,sBAAxBT,mCAAAA,2BAA0BU,KAAK,qBAA/BV,sCAAAA,4BAAkC,+BAClC;QACAmB,gBAAgB;IAClB;IAEA,OAAOA;AACT;AAEO,SAAS5B;QACUS,iCAAAA;IAAxB,MAAMsB,mBAAkBtB,4BAAAA,QAAQQ,GAAG,CAACC,YAAY,sBAAxBT,kCAAAA,0BAA0BU,KAAK,CACrD,2DADsBV,+BAErB,CAAC,EAAE;IAEN,OAAOsB,kBAAkBX,SAASW,iBAAiB,MAAMF;AAC3D"}