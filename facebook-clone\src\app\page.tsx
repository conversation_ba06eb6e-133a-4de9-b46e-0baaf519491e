'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Header from '@/components/Header'
import Sidebar from '@/components/Sidebar'
import NewsFeed from '@/components/NewsFeed'
import RightSidebar from '@/components/RightSidebar'

interface User {
  id: number
  name: string
  avatar: string
  email: string
}

export default function Home() {
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check if user is logged in
    const storedUser = localStorage.getItem('facebookUser')
    
    if (!storedUser) {
      router.push('/login')
      return
    }
    
    setUser(JSON.parse(storedUser))
    setLoading(false)
  }, [router])

  if (loading || !user) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>
  }

  return (
    <div className="min-h-screen bg-facebook-gray">
      <Header user={user} />
      
      <div className="flex max-w-7xl mx-auto pt-16">
        {/* Left Sidebar */}
        <div className="hidden lg:block w-80 fixed left-0 top-16 h-full overflow-y-auto">
          <Sidebar user={user} />
        </div>
        
        {/* Main Content */}
        <div className="flex-1 lg:ml-80 lg:mr-80">
          <NewsFeed user={user} />
        </div>
        
        {/* Right Sidebar */}
        <div className="hidden lg:block w-80 fixed right-0 top-16 h-full overflow-y-auto">
          <RightSidebar />
        </div>
      </div>
    </div>
  )
}
