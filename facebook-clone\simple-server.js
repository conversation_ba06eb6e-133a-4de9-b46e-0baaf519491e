const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
  console.log(`Request: ${req.method} ${req.url}`);
  
  if (req.url === '/' || req.url === '/index.html') {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Clone</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://unpkg.com/lucide-react@latest/dist/umd/lucide-react.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              facebook: {
                blue: '#1877f2',
                'blue-dark': '#166fe5',
                gray: '#f0f2f5',
                'gray-dark': '#e4e6ea',
                'text-gray': '#65676b',
                'text-dark': '#1c1e21',
              }
            }
          }
        }
      }
    </script>
</head>
<body class="bg-gray-100">
    <div id="root"></div>
    
    <script type="text/babel">
      const { useState } = React;
      const { Search, Home, Users, MessageCircle, Bell, Menu, Heart, MoreHorizontal } = lucideReact;

      // Header Component
      function Header({ user }) {
        return (
          <header className="fixed top-0 left-0 right-0 bg-white shadow-md z-50 border-b border-gray-200">
            <div className="flex items-center justify-between px-4 py-2 max-w-7xl mx-auto">
              <div className="flex items-center space-x-4">
                <div className="text-2xl font-bold text-blue-600">
                  facebook
                </div>
                <div className="hidden md:flex relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search Facebook"
                    className="pl-10 pr-4 py-2 bg-gray-100 rounded-full w-64 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="hidden md:flex items-center space-x-2">
                <button className="p-3 rounded-lg hover:bg-gray-100 text-blue-600">
                  <Home className="w-6 h-6" />
                </button>
                <button className="p-3 rounded-lg hover:bg-gray-100 text-gray-600">
                  <Users className="w-6 h-6" />
                </button>
                <button className="p-3 rounded-lg hover:bg-gray-100 text-gray-600">
                  <MessageCircle className="w-6 h-6" />
                </button>
                <button className="p-3 rounded-lg hover:bg-gray-100 text-gray-600">
                  <Bell className="w-6 h-6" />
                </button>
              </div>

              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-100 rounded-lg p-2">
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-8 h-8 rounded-full"
                  />
                  <span className="hidden md:block font-semibold text-gray-800">
                    {user.name}
                  </span>
                </div>
              </div>
            </div>
          </header>
        );
      }

      // Post Component
      function Post({ post, onLike }) {
        return (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-4">
            <div className="flex items-center justify-between p-4">
              <div className="flex items-center space-x-3">
                <img
                  src={post.user.avatar}
                  alt={post.user.name}
                  className="w-10 h-10 rounded-full"
                />
                <div>
                  <div className="font-semibold text-gray-800">
                    {post.user.name}
                  </div>
                  <div className="text-sm text-gray-500">
                    {post.timestamp}
                  </div>
                </div>
              </div>
              <button className="p-2 hover:bg-gray-100 rounded-full">
                <MoreHorizontal className="w-5 h-5 text-gray-500" />
              </button>
            </div>

            <div className="px-4 pb-3">
              <p className="text-gray-800 leading-relaxed">
                {post.content}
              </p>
            </div>

            {post.image && (
              <div className="mb-3">
                <img
                  src={post.image}
                  alt="Post content"
                  className="w-full h-auto max-h-96 object-cover"
                />
              </div>
            )}

            <div className="px-4 py-2 flex items-center justify-between text-sm text-gray-500 border-b border-gray-200">
              <div className="flex items-center space-x-1">
                {post.likes > 0 && (
                  <>
                    <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                      <Heart className="w-3 h-3 text-white fill-current" />
                    </div>
                    <span>{post.likes}</span>
                  </>
                )}
              </div>
              <div className="flex space-x-4">
                {post.comments > 0 && <span>{post.comments} comments</span>}
                {post.shares > 0 && <span>{post.shares} shares</span>}
              </div>
            </div>

            <div className="flex items-center justify-around p-2">
              <button
                onClick={() => onLike(post.id)}
                className={\`flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors flex-1 justify-center \${
                  post.isLiked ? 'text-blue-600' : 'text-gray-500'
                }\`}
              >
                <Heart className={\`w-5 h-5 \${post.isLiked ? 'fill-current' : ''}\`} />
                <span className="font-medium">Like</span>
              </button>
              
              <button className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors flex-1 justify-center text-gray-500">
                <MessageCircle className="w-5 h-5" />
                <span className="font-medium">Comment</span>
              </button>
            </div>
          </div>
        );
      }

      // Main App Component
      function App() {
        const [user] = useState({
          id: 1,
          name: 'John Doe',
          avatar: 'https://via.placeholder.com/40/1877f2/ffffff?text=JD',
          email: '<EMAIL>'
        });

        const [posts, setPosts] = useState([
          {
            id: 1,
            user: {
              name: 'Jane Smith',
              avatar: 'https://via.placeholder.com/40/ff6b6b/ffffff?text=JS'
            },
            content: 'Just finished building an amazing React app! 🚀 The feeling when everything clicks together is incredible. #coding #react #webdev',
            image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
            timestamp: '2 hours ago',
            likes: 24,
            comments: 8,
            shares: 3,
            isLiked: false
          },
          {
            id: 2,
            user: {
              name: 'Mike Johnson',
              avatar: 'https://via.placeholder.com/40/4ecdc4/ffffff?text=MJ'
            },
            content: 'Beautiful sunset from my balcony today. Sometimes you need to pause and appreciate the simple things in life. 🌅',
            image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
            timestamp: '4 hours ago',
            likes: 156,
            comments: 23,
            shares: 12,
            isLiked: true
          }
        ]);

        const handleLike = (postId) => {
          setPosts(posts.map(post => 
            post.id === postId 
              ? { 
                  ...post, 
                  isLiked: !post.isLiked,
                  likes: post.isLiked ? post.likes - 1 : post.likes + 1
                }
              : post
          ));
        };

        return (
          <div className="min-h-screen bg-gray-100">
            <Header user={user} />
            
            <div className="pt-16 max-w-2xl mx-auto p-4">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
                <div className="flex space-x-3">
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-10 h-10 rounded-full"
                  />
                  <button className="flex-1 text-left px-4 py-3 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors text-gray-500">
                    What's on your mind, {user.name.split(' ')[0]}?
                  </button>
                </div>
              </div>
              
              <div className="space-y-4">
                {posts.map(post => (
                  <Post 
                    key={post.id} 
                    post={post} 
                    onLike={handleLike}
                  />
                ))}
              </div>
            </div>
          </div>
        );
      }

      ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
    `);
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
  }
});

const PORT = 3000;
server.listen(PORT, () => {
  console.log(\`🚀 Facebook Clone server running at http://localhost:\${PORT}\`);
  console.log('Press Ctrl+C to stop the server');
});
