{"version": 3, "sources": ["../../../src/lib/memory/shutdown.ts"], "names": ["info", "bold", "getGcEvents", "stopObservingGc", "getAllMemoryUsageSpans", "stopPeriodicMemoryUsageTracing", "disableMemoryDebuggingMode", "gcEvents", "totalTimeInGcMs", "reduce", "acc", "event", "duration", "toFixed", "allMemoryUsage", "peakHeapUsage", "Math", "max", "map", "usage", "peakRssUsage"], "mappings": "AAAA,SAASA,IAAI,QAAQ,yBAAwB;AAC7C,SAASC,IAAI,QAAQ,gBAAe;AACpC,SAASC,WAAW,EAAEC,eAAe,QAAQ,gBAAe;AAC5D,SAASC,sBAAsB,EAAEC,8BAA8B,QAAQ,UAAS;AAEhF,OAAO,SAASC;IACdD;IACAF;IAEAH,KAAKC,KAAK;IAEV,MAAMM,WAAWL;IACjB,MAAMM,kBAAkBD,SAASE,MAAM,CACrC,CAACC,KAAKC,QAAUD,MAAMC,MAAMC,QAAQ,EACpC;IAEFZ,KAAK,CAAC,2BAA2B,EAAEQ,gBAAgBK,OAAO,CAAC,GAAG,EAAE,CAAC;IAEjE,MAAMC,iBAAiBV;IACvB,MAAMW,gBAAgBC,KAAKC,GAAG,IACzBH,eAAeI,GAAG,CAAC,CAACC,QAAUA,KAAK,CAAC,kBAAkB;IAE3D,MAAMC,eAAeJ,KAAKC,GAAG,IACxBH,eAAeI,GAAG,CAAC,CAACC,QAAUA,KAAK,CAAC,aAAa;IAEtDnB,KAAK,CAAC,oBAAoB,EAAE,AAACe,CAAAA,gBAAgB,OAAO,IAAG,EAAGF,OAAO,CAAC,GAAG,GAAG,CAAC;IACzEb,KAAK,CAAC,mBAAmB,EAAE,AAACoB,CAAAA,eAAe,OAAO,IAAG,EAAGP,OAAO,CAAC,GAAG,GAAG,CAAC;AACzE"}