{"name": "@next/eslint-plugin-next", "version": "14.2.5", "description": "ESLint plugin for Next.js.", "main": "dist/index.js", "license": "MIT", "repository": {"url": "vercel/next.js", "directory": "packages/eslint-plugin-next"}, "files": ["dist"], "dependencies": {"glob": "10.3.10"}, "devDependencies": {"eslint": "7.24.0"}, "scripts": {"build": "swc -d dist src", "prepublishOnly": "cd ../../ && turbo run build"}}