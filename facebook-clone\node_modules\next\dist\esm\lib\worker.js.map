{"version": 3, "sources": ["../../src/lib/worker.ts"], "names": ["Worker", "JestWorker", "getNodeOptionsWithoutInspect", "RESTARTED", "Symbol", "cleanupWorkers", "worker", "cur<PERSON><PERSON><PERSON>", "_workerPool", "_workers", "_child", "kill", "constructor", "worker<PERSON><PERSON>", "options", "timeout", "onRestart", "logger", "console", "farmOptions", "restartPromise", "resolveRestartPromise", "activeTasks", "_worker", "undefined", "createWorker", "forkOptions", "env", "process", "NODE_OPTIONS", "replace", "trim", "Promise", "resolve", "enableWorkerThreads", "on", "code", "signal", "error", "getStdout", "pipe", "stdout", "getStderr", "stderr", "onHanging", "warn", "end", "then", "hanging<PERSON><PERSON>r", "onActivity", "clearTimeout", "setTimeout", "method", "exposedMethods", "startsWith", "args", "attempts", "result", "race", "bind", "Error", "close"], "mappings": "AACA,SAASA,UAAUC,UAAU,QAAQ,iCAAgC;AACrE,SAASC,4BAA4B,QAAQ,sBAAqB;AAGlE,MAAMC,YAAYC,OAAO;AAEzB,MAAMC,iBAAiB,CAACC;QACG;IAAzB,KAAK,MAAMC,aAAc,EAAA,sBAAA,AAACD,OAAeE,WAAW,qBAA3B,oBAA6BC,QAAQ,KAAI,EAAE,CAE/D;YACHF;SAAAA,oBAAAA,UAAUG,MAAM,qBAAhBH,kBAAkBI,IAAI,CAAC;IACzB;AACF;AAEA,OAAO,MAAMX;IAGXY,YACEC,UAAkB,EAClBC,OAMC,CACD;QACA,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAASC,OAAO,EAAE,GAAGC,aAAa,GAAGL;QAE/D,IAAIM;QACJ,IAAIC;QACJ,IAAIC,cAAc;QAElB,IAAI,CAACC,OAAO,GAAGC;QAEf,MAAMC,eAAe;gBAMRN;YALX,IAAI,CAACI,OAAO,GAAG,IAAItB,WAAWY,YAAY;gBACxC,GAAGM,WAAW;gBACdO,aAAa;oBACX,GAAGP,YAAYO,WAAW;oBAC1BC,KAAK;wBACH,GAAKR,EAAAA,2BAAAA,YAAYO,WAAW,qBAAvBP,yBAAyBQ,GAAG,KAAI,CAAC,CAAC;wBACvC,GAAGC,QAAQD,GAAG;wBACd,4CAA4C;wBAC5C,qBAAqB;wBACrBE,cAAc3B,+BACX4B,OAAO,CAAC,iCAAiC,IACzCC,IAAI;oBACT;gBACF;YACF;YACAX,iBAAiB,IAAIY,QACnB,CAACC,UAAaZ,wBAAwBY;YAGxC;;;;;;;;OAQC,GACD,IAAI,CAACd,YAAYe,mBAAmB,EAAE;oBACd;gBAAtB,KAAK,MAAM5B,UAAW,EAAA,4BAAA,AAAC,IAAI,CAACiB,OAAO,CAASf,WAAW,qBAAjC,0BAAmCC,QAAQ,KAC/D,EAAE,CAEC;wBACHH;qBAAAA,iBAAAA,OAAOI,MAAM,qBAAbJ,eAAe6B,EAAE,CAAC,QAAQ,CAACC,MAAMC;wBAC/B,IAAI,AAACD,CAAAA,QAASC,UAAUA,WAAW,QAAQ,KAAM,IAAI,CAACd,OAAO,EAAE;4BAC7DN,OAAOqB,KAAK,CACV,CAAC,gCAAgC,EAAEF,KAAK,aAAa,EAAEC,OAAO,CAAC;wBAEnE;oBACF;gBACF;YACF;YAEA,IAAI,CAACd,OAAO,CAACgB,SAAS,GAAGC,IAAI,CAACZ,QAAQa,MAAM;YAC5C,IAAI,CAAClB,OAAO,CAACmB,SAAS,GAAGF,IAAI,CAACZ,QAAQe,MAAM;QAC9C;QACAlB;QAEA,MAAMmB,YAAY;YAChB,MAAMtC,SAAS,IAAI,CAACiB,OAAO;YAC3B,IAAI,CAACjB,QAAQ;YACb,MAAM2B,UAAUZ;YAChBI;YACAR,OAAO4B,IAAI,CACT,CAAC,sDAAsD,EACrD9B,UAAU,CAAC,IAAI,EAAEA,UAAU,KAAK,QAAQ,CAAC,GAAG,GAC7C,0DAA0D,CAAC;YAE9DT,OAAOwC,GAAG,GAAGC,IAAI,CAAC;gBAChBd,QAAQ9B;YACV;QACF;QAEA,IAAI6C,eAAuC;QAE3C,MAAMC,aAAa;YACjB,IAAID,cAAcE,aAAaF;YAC/BA,eAAe1B,cAAc,KAAK6B,WAAWP,WAAW7B;QAC1D;QAEA,KAAK,MAAMqC,UAAUjC,YAAYkC,cAAc,CAAE;YAC/C,IAAID,OAAOE,UAAU,CAAC,MAAM;YAC3B,AAAC,IAAI,AAAQ,CAACF,OAAO,GAAGrC,UAErB,OAAO,GAAGwC;gBACRjC;gBACA,IAAI;oBACF,IAAIkC,WAAW;oBACf,OAAS;wBACPP;wBACA,MAAMQ,SAAS,MAAMzB,QAAQ0B,IAAI,CAAC;4BAC/B,IAAI,CAACnC,OAAO,AAAQ,CAAC6B,OAAO,IAAIG;4BACjCnC;yBACD;wBACD,IAAIqC,WAAWtD,WAAW,OAAOsD;wBACjC,IAAIzC,WAAWA,UAAUoC,QAAQG,MAAM,EAAEC;oBAC3C;gBACF,SAAU;oBACRlC;oBACA2B;gBACF;YACF,IACA,AAAC,IAAI,CAAC1B,OAAO,AAAQ,CAAC6B,OAAO,CAACO,IAAI,CAAC,IAAI,CAACpC,OAAO;QACrD;IACF;IAEAuB,MAAqC;QACnC,MAAMxC,SAAS,IAAI,CAACiB,OAAO;QAC3B,IAAI,CAACjB,QAAQ;YACX,MAAM,IAAIsD,MAAM;QAClB;QACAvD,eAAeC;QACf,IAAI,CAACiB,OAAO,GAAGC;QACf,OAAOlB,OAAOwC,GAAG;IACnB;IAEA;;GAEC,GACDe,QAAc;QACZ,IAAI,IAAI,CAACtC,OAAO,EAAE;YAChBlB,eAAe,IAAI,CAACkB,OAAO;YAC3B,IAAI,CAACA,OAAO,CAACuB,GAAG;QAClB;IACF;AACF"}