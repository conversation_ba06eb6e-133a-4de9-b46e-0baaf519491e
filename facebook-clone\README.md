# Facebook Clone

A Facebook clone built with Next.js, TypeScript, and Tailwind CSS.

## Features

- **User Authentication**: Login and registration system
- **News Feed**: View and interact with posts
- **Create Posts**: Share text and image posts
- **Like & Comment**: Interact with posts
- **User Profile**: View and edit user profiles
- **Notifications**: Receive and view notifications
- **Responsive Design**: Works on mobile and desktop

## Tech Stack

- **Frontend Framework**: Next.js 14
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **State Management**: React Hooks

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/facebook-clone.git
cd facebook-clone
```

2. Install dependencies
```bash
npm install
# or
yarn install
```

3. Run the development server
```bash
npm run dev
# or
yarn dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Project Structure

```
facebook-clone/
├── src/
│   ├── app/                  # Next.js app directory
│   │   ├── login/            # Login page
│   │   ├── profile/          # Profile page
│   │   ├── register/         # Registration page
│   │   ├── globals.css       # Global styles
│   │   ├── layout.tsx        # Root layout
│   │   └── page.tsx          # Home page
│   └── components/           # React components
│       ├── CommentSection.tsx
│       ├── CreatePost.tsx
│       ├── Header.tsx
│       ├── NewsFeed.tsx
│       ├── Notifications.tsx
│       ├── Post.tsx
│       ├── RightSidebar.tsx
│       └── Sidebar.tsx
├── public/                   # Static files
├── .eslintrc.json            # ESLint configuration
├── next.config.js            # Next.js configuration
├── package.json              # Project dependencies
├── postcss.config.js         # PostCSS configuration
├── tailwind.config.js        # Tailwind CSS configuration
└── tsconfig.json             # TypeScript configuration
```

## Authentication

This demo uses localStorage for authentication. In a production environment, you would use a more secure method like JWT tokens, cookies, or an authentication service.

## License

This project is for educational purposes only.