-- =====================================================================
-- Commands for table: CloudAppEvents
-- =====================================================================

-- 1. Command to Create Destination Table
.create table CloudAppEvents (Timestamp:datetime, ActionType:string, Application:string, ApplicationId:int, AppInstanceId:int, AccountObjectId:string, AccountId:string, AccountDisplayName:string, IsAdminOperation:bool, DeviceType:string, OSPlatform:string, IPAddress:string, IsAnonymousProxy:bool, CountryCode:string, City:string, Isp:string, UserAgent:string, ActivityType:string, ActivityObjects:dynamic, ObjectName:string, ObjectType:string, ObjectId:string, ReportId:string, AccountType:string, IsExternalUser:bool, IsImpersonated:bool, IPTags:dynamic, IPCategory:string, UserAgentTags:dynamic, RawEventData:dynamic, AdditionalFields:dynamic, LastSeenForUser:dynamic, UncommonForUser:dynamic, AuditSource:string, SessionData:dynamic, OAuthAppId:string)

-- 2. Command to Create Transformation Function
.create-or-alter function CloudAppEventsExpand {
CloudAppEventsRaw
| mv-expand events = records
| project Timestamp = todatetime(events.properties.Timestamp),
    ActionType = tostring(events.properties.ActionType),
    Application = tostring(events.properties.Application),
    ApplicationId = toint(events.properties.ApplicationId),
    AppInstanceId = toint(events.properties.AppInstanceId),
    AccountObjectId = tostring(events.properties.AccountObjectId),
    AccountId = tostring(events.properties.AccountId),
    AccountDisplayName = tostring(events.properties.AccountDisplayName),
    IsAdminOperation = tobool(events.properties.IsAdminOperation),
    DeviceType = tostring(events.properties.DeviceType),
    OSPlatform = tostring(events.properties.OSPlatform),
    IPAddress = tostring(events.properties.IPAddress),
    IsAnonymousProxy = tobool(events.properties.IsAnonymousProxy),
    CountryCode = tostring(events.properties.CountryCode),
    City = tostring(events.properties.City),
    Isp = tostring(events.properties.Isp),
    UserAgent = tostring(events.properties.UserAgent),
    ActivityType = tostring(events.properties.ActivityType),
    ActivityObjects = todynamic(events.properties.ActivityObjects),
    ObjectName = tostring(events.properties.ObjectName),
    ObjectType = tostring(events.properties.ObjectType),
    ObjectId = tostring(events.properties.ObjectId),
    ReportId = tostring(events.properties.ReportId),
    AccountType = tostring(events.properties.AccountType),
    IsExternalUser = tobool(events.properties.IsExternalUser),
    IsImpersonated = tobool(events.properties.IsImpersonated),
    IPTags = todynamic(events.properties.IPTags),
    IPCategory = tostring(events.properties.IPCategory),
    UserAgentTags = todynamic(events.properties.UserAgentTags),
    RawEventData = todynamic(events.properties.RawEventData),
    AdditionalFields = todynamic(events.properties.AdditionalFields),
    LastSeenForUser = todynamic(events.properties.LastSeenForUser),
    UncommonForUser = todynamic(events.properties.UncommonForUser),
    AuditSource = tostring(events.properties.AuditSource),
    SessionData = todynamic(events.properties.SessionData),
    OAuthAppId = tostring(events.properties.OAuthAppId)
}

-- 3. Command to Set the Update Policy on the Table
.alter table CloudAppEvents policy update @'[{"Source": "CloudAppEventsRaw", "Query": "CloudAppEventsExpand()", "IsEnabled": "True", "IsTransactional": "true"}]'

-- =====================================================================
-- Commands for table: EmailAttachmentInfo
-- =====================================================================

-- 1. Command to Create Destination Table
.create table EmailAttachmentInfo (Timestamp:datetime, NetworkMessageId:string, SenderFromAddress:string, SenderDisplayName:string, SenderObjectId:string, RecipientEmailAddress:string, RecipientObjectId:string, FileName:string, FileType:string, SHA256:string, FileSize:long, ThreatTypes:string, ThreatNames:string, DetectionMethods:string, ReportId:string)

-- 2. Command to Create Transformation Function
.create-or-alter function EmailAttachmentInfoExpand {
EmailAttachmentInfoRaw
| mv-expand events = records
| project Timestamp = todatetime(events.properties.Timestamp),
    NetworkMessageId = tostring(events.properties.NetworkMessageId),
    SenderFromAddress = tostring(events.properties.SenderFromAddress),
    SenderDisplayName = tostring(events.properties.SenderDisplayName),
    SenderObjectId = tostring(events.properties.SenderObjectId),
    RecipientEmailAddress = tostring(events.properties.RecipientEmailAddress),
    RecipientObjectId = tostring(events.properties.RecipientObjectId),
    FileName = tostring(events.properties.FileName),
    FileType = tostring(events.properties.FileType),
    SHA256 = tostring(events.properties.SHA256),
    FileSize = tolong(events.properties.FileSize),
    ThreatTypes = tostring(events.properties.ThreatTypes),
    ThreatNames = tostring(events.properties.ThreatNames),
    DetectionMethods = tostring(events.properties.DetectionMethods),
    ReportId = tostring(events.properties.ReportId)
}

-- 3. Command to Set the Update Policy on the Table
.alter table EmailAttachmentInfo policy update @'[{"Source": "EmailAttachmentInfoRaw", "Query": "EmailAttachmentInfoExpand()", "IsEnabled": "True", "IsTransactional": "true"}]'

-- =====================================================================
-- Commands for table: EmailEvents
-- =====================================================================

-- 1. Command to Create Destination Table
.create table EmailEvents (Timestamp:datetime, NetworkMessageId:string, InternetMessageId:string, SenderMailFromAddress:string, SenderFromAddress:string, SenderDisplayName:string, SenderObjectId:string, SenderMailFromDomain:string, SenderFromDomain:string, SenderIPv4:string, SenderIPv6:string, RecipientEmailAddress:string, RecipientObjectId:string, Subject:string, EmailClusterId:long, EmailDirection:string, DeliveryAction:string, DeliveryLocation:string, ThreatTypes:string, ThreatNames:string, DetectionMethods:string, ConfidenceLevel:string, BulkComplaintLevel:int, EmailAction:string, EmailActionPolicy:string, EmailActionPolicyGuid:string, AuthenticationDetails:string, AttachmentCount:int, UrlCount:int, EmailLanguage:string, Connectors:string, OrgLevelAction:string, OrgLevelPolicy:string, UserLevelAction:string, UserLevelPolicy:string, ReportId:string, AdditionalFields:string, LatestDeliveryLocation*:string, LatestDeliveryAction*:string)

-- 2. Command to Create Transformation Function
.create-or-alter function EmailEventsExpand {
EmailEventsRaw
| mv-expand events = records
| project Timestamp = todatetime(events.properties.Timestamp),
    NetworkMessageId = tostring(events.properties.NetworkMessageId),
    InternetMessageId = tostring(events.properties.InternetMessageId),
    SenderMailFromAddress = tostring(events.properties.SenderMailFromAddress),
    SenderFromAddress = tostring(events.properties.SenderFromAddress),
    SenderDisplayName = tostring(events.properties.SenderDisplayName),
    SenderObjectId = tostring(events.properties.SenderObjectId),
    SenderMailFromDomain = tostring(events.properties.SenderMailFromDomain),
    SenderFromDomain = tostring(events.properties.SenderFromDomain),
    SenderIPv4 = tostring(events.properties.SenderIPv4),
    SenderIPv6 = tostring(events.properties.SenderIPv6),
    RecipientEmailAddress = tostring(events.properties.RecipientEmailAddress),
    RecipientObjectId = tostring(events.properties.RecipientObjectId),
    Subject = tostring(events.properties.Subject),
    EmailClusterId = tolong(events.properties.EmailClusterId),
    EmailDirection = tostring(events.properties.EmailDirection),
    DeliveryAction = tostring(events.properties.DeliveryAction),
    DeliveryLocation = tostring(events.properties.DeliveryLocation),
    ThreatTypes = tostring(events.properties.ThreatTypes),
    ThreatNames = tostring(events.properties.ThreatNames),
    DetectionMethods = tostring(events.properties.DetectionMethods),
    ConfidenceLevel = tostring(events.properties.ConfidenceLevel),
    BulkComplaintLevel = toint(events.properties.BulkComplaintLevel),
    EmailAction = tostring(events.properties.EmailAction),
    EmailActionPolicy = tostring(events.properties.EmailActionPolicy),
    EmailActionPolicyGuid = tostring(events.properties.EmailActionPolicyGuid),
    AuthenticationDetails = tostring(events.properties.AuthenticationDetails),
    AttachmentCount = toint(events.properties.AttachmentCount),
    UrlCount = toint(events.properties.UrlCount),
    EmailLanguage = tostring(events.properties.EmailLanguage),
    Connectors = tostring(events.properties.Connectors),
    OrgLevelAction = tostring(events.properties.OrgLevelAction),
    OrgLevelPolicy = tostring(events.properties.OrgLevelPolicy),
    UserLevelAction = tostring(events.properties.UserLevelAction),
    UserLevelPolicy = tostring(events.properties.UserLevelPolicy),
    ReportId = tostring(events.properties.ReportId),
    AdditionalFields = tostring(events.properties.AdditionalFields),
    LatestDeliveryLocation* = tostring(events.properties.LatestDeliveryLocation*),
    LatestDeliveryAction* = tostring(events.properties.LatestDeliveryAction*)
}

-- 3. Command to Set the Update Policy on the Table
.alter table EmailEvents policy update @'[{"Source": "EmailEventsRaw", "Query": "EmailEventsExpand()", "IsEnabled": "True", "IsTransactional": "true"}]'

-- =====================================================================
-- Commands for table: EmailPostDeliveryEvents
-- =====================================================================

-- 1. Command to Create Destination Table
.create table EmailPostDeliveryEvents (Timestamp:datetime, NetworkMessageId:string, InternetMessageId:string, Action:string, ActionType:string, ActionTrigger:string, ActionResult:string, RecipientEmailAddress:string, DeliveryLocation:string, ThreatTypes:string, DetectionMethods:string, ReportId:string)

-- 2. Command to Create Transformation Function
.create-or-alter function EmailPostDeliveryEventsExpand {
EmailPostDeliveryEventsRaw
| mv-expand events = records
| project Timestamp = todatetime(events.properties.Timestamp),
    NetworkMessageId = tostring(events.properties.NetworkMessageId),
    InternetMessageId = tostring(events.properties.InternetMessageId),
    Action = tostring(events.properties.Action),
    ActionType = tostring(events.properties.ActionType),
    ActionTrigger = tostring(events.properties.ActionTrigger),
    ActionResult = tostring(events.properties.ActionResult),
    RecipientEmailAddress = tostring(events.properties.RecipientEmailAddress),
    DeliveryLocation = tostring(events.properties.DeliveryLocation),
    ThreatTypes = tostring(events.properties.ThreatTypes),
    DetectionMethods = tostring(events.properties.DetectionMethods),
    ReportId = tostring(events.properties.ReportId)
}

-- 3. Command to Set the Update Policy on the Table
.alter table EmailPostDeliveryEvents policy update @'[{"Source": "EmailPostDeliveryEventsRaw", "Query": "EmailPostDeliveryEventsExpand()", "IsEnabled": "True", "IsTransactional": "true"}]'

-- =====================================================================
-- Commands for table: EmailUrlInfo
-- =====================================================================

-- 1. Command to Create Destination Table
.create table EmailUrlInfo (Timestamp:datetime, NetworkMessageId:string, Url:string, UrlDomain:string, UrlLocation:string, ReportId:string)

-- 2. Command to Create Transformation Function
.create-or-alter function EmailUrlInfoExpand {
EmailUrlInfoRaw
| mv-expand events = records
| project Timestamp = todatetime(events.properties.Timestamp),
    NetworkMessageId = tostring(events.properties.NetworkMessageId),
    Url = tostring(events.properties.Url),
    UrlDomain = tostring(events.properties.UrlDomain),
    UrlLocation = tostring(events.properties.UrlLocation),
    ReportId = tostring(events.properties.ReportId)
}

-- 3. Command to Set the Update Policy on the Table
.alter table EmailUrlInfo policy update @'[{"Source": "EmailUrlInfoRaw", "Query": "EmailUrlInfoExpand()", "IsEnabled": "True", "IsTransactional": "true"}]'

-- =====================================================================
-- Commands for table: IdentityDirectoryEvents
-- =====================================================================

-- 1. Command to Create Destination Table
.create table IdentityDirectoryEvents (Timestamp:datetime, ActionType:string, Application:string, TargetAccountUpn:string, TargetAccountDisplayName:string, TargetDeviceName:string, DestinationDeviceName:string, DestinationIPAddress:string, DestinationPort:int, Protocol:string, AccountName:string, AccountDomain:string, AccountUpn:string, AccountSid:string, AccountObjectId:string, AccountDisplayName:string, DeviceName:string, IPAddress:string, Port:int, Location:string, ISP:string, ReportId:string, AdditionalFields:dynamic)

-- 2. Command to Create Transformation Function
.create-or-alter function IdentityDirectoryEventsExpand {
IdentityDirectoryEventsRaw
| mv-expand events = records
| project Timestamp = todatetime(events.properties.Timestamp),
    ActionType = tostring(events.properties.ActionType),
    Application = tostring(events.properties.Application),
    TargetAccountUpn = tostring(events.properties.TargetAccountUpn),
    TargetAccountDisplayName = tostring(events.properties.TargetAccountDisplayName),
    TargetDeviceName = tostring(events.properties.TargetDeviceName),
    DestinationDeviceName = tostring(events.properties.DestinationDeviceName),
    DestinationIPAddress = tostring(events.properties.DestinationIPAddress),
    DestinationPort = toint(events.properties.DestinationPort),
    Protocol = tostring(events.properties.Protocol),
    AccountName = tostring(events.properties.AccountName),
    AccountDomain = tostring(events.properties.AccountDomain),
    AccountUpn = tostring(events.properties.AccountUpn),
    AccountSid = tostring(events.properties.AccountSid),
    AccountObjectId = tostring(events.properties.AccountObjectId),
    AccountDisplayName = tostring(events.properties.AccountDisplayName),
    DeviceName = tostring(events.properties.DeviceName),
    IPAddress = tostring(events.properties.IPAddress),
    Port = toint(events.properties.Port),
    Location = tostring(events.properties.Location),
    ISP = tostring(events.properties.ISP),
    ReportId = tostring(events.properties.ReportId),
    AdditionalFields = todynamic(events.properties.AdditionalFields)
}

-- 3. Command to Set the Update Policy on the Table
.alter table IdentityDirectoryEvents policy update @'[{"Source": "IdentityDirectoryEventsRaw", "Query": "IdentityDirectoryEventsExpand()", "IsEnabled": "True", "IsTransactional": "true"}]'

-- =====================================================================
-- Commands for table: IdentityInfo
-- =====================================================================

-- 1. Command to Create Destination Table
.create table IdentityInfo (Timestamp*:datetime, ReportId*:string, AccountObjectId:string, AccountUpn:string, OnPremSid:string, AccountDisplayName:string, AccountName:string, AccountDomain*:string, CriticalityLevel:int, Type*:string, DistinguishedName*:string, CloudSid:string, GivenName:string, Surname:string, Department:string, JobTitle:string, EmailAddress:string, SipProxyAddress:string, Address:string, City:string, Country:string, IsAccountEnabled:bool, Manager*:string, Phone*:string, CreatedDateTime*:datetime, ChangeSource*:string, BlastRadius:string, CompanyName:string, DeletedDateTime:datetime, EmployeeId:string, OtherMailAddresses:dynamic, RiskLevel:string, RiskLevelDetails:string, State:string, Tags*:dynamic, AssignedRoles*:dynamic, PrivilegedEntraPimRoles(Preview)**:dynamic, TenantId:string, SourceSystem*:string, OnPremObjectId:string, TenantMembershipType:string, RiskStatus:string, UserAccountControl:string, IdentityEnvironment:string, SourceProviders:dynamic, GroupMembership:dynamic)

-- 2. Command to Create Transformation Function
.create-or-alter function IdentityInfoExpand {
IdentityInfoRaw
| mv-expand events = records
| project Timestamp* = todatetime(events.properties.Timestamp*),
    ReportId* = tostring(events.properties.ReportId*),
    AccountObjectId = tostring(events.properties.AccountObjectId),
    AccountUpn = tostring(events.properties.AccountUpn),
    OnPremSid = tostring(events.properties.OnPremSid),
    AccountDisplayName = tostring(events.properties.AccountDisplayName),
    AccountName = tostring(events.properties.AccountName),
    AccountDomain* = tostring(events.properties.AccountDomain*),
    CriticalityLevel = toint(events.properties.CriticalityLevel),
    Type* = tostring(events.properties.Type*),
    DistinguishedName* = tostring(events.properties.DistinguishedName*),
    CloudSid = tostring(events.properties.CloudSid),
    GivenName = tostring(events.properties.GivenName),
    Surname = tostring(events.properties.Surname),
    Department = tostring(events.properties.Department),
    JobTitle = tostring(events.properties.JobTitle),
    EmailAddress = tostring(events.properties.EmailAddress),
    SipProxyAddress = tostring(events.properties.SipProxyAddress),
    Address = tostring(events.properties.Address),
    City = tostring(events.properties.City),
    Country = tostring(events.properties.Country),
    IsAccountEnabled = tobool(events.properties.IsAccountEnabled),
    Manager* = tostring(events.properties.Manager*),
    Phone* = tostring(events.properties.Phone*),
    CreatedDateTime* = todatetime(events.properties.CreatedDateTime*),
    ChangeSource* = tostring(events.properties.ChangeSource*),
    BlastRadius = tostring(events.properties.BlastRadius),
    CompanyName = tostring(events.properties.CompanyName),
    DeletedDateTime = todatetime(events.properties.DeletedDateTime),
    EmployeeId = tostring(events.properties.EmployeeId),
    OtherMailAddresses = todynamic(events.properties.OtherMailAddresses),
    RiskLevel = tostring(events.properties.RiskLevel),
    RiskLevelDetails = tostring(events.properties.RiskLevelDetails),
    State = tostring(events.properties.State),
    Tags* = todynamic(events.properties.Tags*),
    AssignedRoles* = todynamic(events.properties.AssignedRoles*),
    PrivilegedEntraPimRoles(Preview)** = todynamic(events.properties.PrivilegedEntraPimRoles(Preview)**),
    TenantId = tostring(events.properties.TenantId),
    SourceSystem* = tostring(events.properties.SourceSystem*),
    OnPremObjectId = tostring(events.properties.OnPremObjectId),
    TenantMembershipType = tostring(events.properties.TenantMembershipType),
    RiskStatus = tostring(events.properties.RiskStatus),
    UserAccountControl = tostring(events.properties.UserAccountControl),
    IdentityEnvironment = tostring(events.properties.IdentityEnvironment),
    SourceProviders = todynamic(events.properties.SourceProviders),
    GroupMembership = todynamic(events.properties.GroupMembership)
}

-- 3. Command to Set the Update Policy on the Table
.alter table IdentityInfo policy update @'[{"Source": "IdentityInfoRaw", "Query": "IdentityInfoExpand()", "IsEnabled": "True", "IsTransactional": "true"}]'

-- =====================================================================
-- Commands for table: IdentityLogonEvents
-- =====================================================================

-- 1. Command to Create Destination Table
.create table IdentityLogonEvents (Timestamp:datetime, ActionType:string, Application:string, LogonType:string, Protocol:string, FailureReason:string, AccountName:string, AccountDomain:string, AccountUpn:string, AccountSid:string, AccountObjectId:string, AccountDisplayName:string, DeviceName:string, DeviceType:string, OSPlatform:string, IPAddress:string, Port:int, DestinationDeviceName:string, DestinationIPAddress:string, DestinationPort:int, TargetDeviceName:string, TargetAccountDisplayName:string, Location:string, Isp:string, ReportId:string, AdditionalFields:dynamic)

-- 2. Command to Create Transformation Function
.create-or-alter function IdentityLogonEventsExpand {
IdentityLogonEventsRaw
| mv-expand events = records
| project Timestamp = todatetime(events.properties.Timestamp),
    ActionType = tostring(events.properties.ActionType),
    Application = tostring(events.properties.Application),
    LogonType = tostring(events.properties.LogonType),
    Protocol = tostring(events.properties.Protocol),
    FailureReason = tostring(events.properties.FailureReason),
    AccountName = tostring(events.properties.AccountName),
    AccountDomain = tostring(events.properties.AccountDomain),
    AccountUpn = tostring(events.properties.AccountUpn),
    AccountSid = tostring(events.properties.AccountSid),
    AccountObjectId = tostring(events.properties.AccountObjectId),
    AccountDisplayName = tostring(events.properties.AccountDisplayName),
    DeviceName = tostring(events.properties.DeviceName),
    DeviceType = tostring(events.properties.DeviceType),
    OSPlatform = tostring(events.properties.OSPlatform),
    IPAddress = tostring(events.properties.IPAddress),
    Port = toint(events.properties.Port),
    DestinationDeviceName = tostring(events.properties.DestinationDeviceName),
    DestinationIPAddress = tostring(events.properties.DestinationIPAddress),
    DestinationPort = toint(events.properties.DestinationPort),
    TargetDeviceName = tostring(events.properties.TargetDeviceName),
    TargetAccountDisplayName = tostring(events.properties.TargetAccountDisplayName),
    Location = tostring(events.properties.Location),
    Isp = tostring(events.properties.Isp),
    ReportId = tostring(events.properties.ReportId),
    AdditionalFields = todynamic(events.properties.AdditionalFields)
}

-- 3. Command to Set the Update Policy on the Table
.alter table IdentityLogonEvents policy update @'[{"Source": "IdentityLogonEventsRaw", "Query": "IdentityLogonEventsExpand()", "IsEnabled": "True", "IsTransactional": "true"}]'

-- =====================================================================
-- Commands for table: IdentityQueryEvents
-- =====================================================================

-- 1. Command to Create Destination Table
.create table IdentityQueryEvents (Timestamp:datetime, ActionType:string, Application:string, QueryType:string, QueryTarget:string, Query:string, Protocol:string, AccountName:string, AccountDomain:string, AccountUpn:string, AccountSid:string, AccountObjectId:string, AccountDisplayName:string, DeviceName:string, IPAddress:string, Port:int, DestinationDeviceName:string, DestinationIPAddress:string, DestinationPort:int, TargetDeviceName:string, TargetAccountUpn:string, TargetAccountDisplayName:string, Location:string, ReportId:string, AdditionalFields:dynamic)

-- 2. Command to Create Transformation Function
.create-or-alter function IdentityQueryEventsExpand {
IdentityQueryEventsRaw
| mv-expand events = records
| project Timestamp = todatetime(events.properties.Timestamp),
    ActionType = tostring(events.properties.ActionType),
    Application = tostring(events.properties.Application),
    QueryType = tostring(events.properties.QueryType),
    QueryTarget = tostring(events.properties.QueryTarget),
    Query = tostring(events.properties.Query),
    Protocol = tostring(events.properties.Protocol),
    AccountName = tostring(events.properties.AccountName),
    AccountDomain = tostring(events.properties.AccountDomain),
    AccountUpn = tostring(events.properties.AccountUpn),
    AccountSid = tostring(events.properties.AccountSid),
    AccountObjectId = tostring(events.properties.AccountObjectId),
    AccountDisplayName = tostring(events.properties.AccountDisplayName),
    DeviceName = tostring(events.properties.DeviceName),
    IPAddress = tostring(events.properties.IPAddress),
    Port = toint(events.properties.Port),
    DestinationDeviceName = tostring(events.properties.DestinationDeviceName),
    DestinationIPAddress = tostring(events.properties.DestinationIPAddress),
    DestinationPort = toint(events.properties.DestinationPort),
    TargetDeviceName = tostring(events.properties.TargetDeviceName),
    TargetAccountUpn = tostring(events.properties.TargetAccountUpn),
    TargetAccountDisplayName = tostring(events.properties.TargetAccountDisplayName),
    Location = tostring(events.properties.Location),
    ReportId = tostring(events.properties.ReportId),
    AdditionalFields = todynamic(events.properties.AdditionalFields)
}

-- 3. Command to Set the Update Policy on the Table
.alter table IdentityQueryEvents policy update @'[{"Source": "IdentityQueryEventsRaw", "Query": "IdentityQueryEventsExpand()", "IsEnabled": "True", "IsTransactional": "true"}]'

-- =====================================================================
-- Commands for table: UrlClickEvents
-- =====================================================================

-- 1. Command to Create Destination Table
.create table UrlClickEvents (Timestamp:datetime, Url:string, ActionType:string, AccountUpn:string, Workload:string, NetworkMessageId:string, ThreatTypes:string, DetectionMethods:string, IPAddress:string, IsClickedThrough:bool, UrlChain:string, ReportId:string)

-- 2. Command to Create Transformation Function
.create-or-alter function UrlClickEventsExpand {
UrlClickEventsRaw
| mv-expand events = records
| project Timestamp = todatetime(events.properties.Timestamp),
    Url = tostring(events.properties.Url),
    ActionType = tostring(events.properties.ActionType),
    AccountUpn = tostring(events.properties.AccountUpn),
    Workload = tostring(events.properties.Workload),
    NetworkMessageId = tostring(events.properties.NetworkMessageId),
    ThreatTypes = tostring(events.properties.ThreatTypes),
    DetectionMethods = tostring(events.properties.DetectionMethods),
    IPAddress = tostring(events.properties.IPAddress),
    IsClickedThrough = tobool(events.properties.IsClickedThrough),
    UrlChain = tostring(events.properties.UrlChain),
    ReportId = tostring(events.properties.ReportId)
}

-- 3. Command to Set the Update Policy on the Table
.alter table UrlClickEvents policy update @'[{"Source": "UrlClickEventsRaw", "Query": "UrlClickEventsExpand()", "IsEnabled": "True", "IsTransactional": "true"}]'
