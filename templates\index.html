{% extends "base.html" %}

{% block title %}Netflix Clone - Home{% endblock %}

{% block content %}
<div class="hero-section">
    <div class="hero-content">
        <h1 class="hero-title">Unlimited movies, TV shows, and more.</h1>
        <p class="hero-subtitle">Watch anywhere. Cancel anytime.</p>
        <button class="hero-button" onclick="scrollToMovies()">Get Started</button>
    </div>
</div>

<div class="movies-section" id="movies">
    <div class="container">
        <h2 class="section-title">Popular Movies</h2>
        <div class="movies-grid">
            {% for movie in movies %}
            <div class="movie-card" onclick="openMovieModal({{ movie.id }})">
                <img src="{{ movie.thumbnail }}" alt="{{ movie.title }}" class="movie-poster">
                <div class="movie-info">
                    <h3 class="movie-title">{{ movie.title }}</h3>
                    <p class="movie-year">{{ movie.year }}</p>
                    <p class="movie-genre">{{ movie.genre }}</p>
                </div>
                <div class="movie-overlay">
                    <button class="play-button" onclick="event.stopPropagation(); watchMovie({{ movie.id }})">
                        ▶ Play
                    </button>
                    <button class="info-button" onclick="event.stopPropagation(); openMovieModal({{ movie.id }})">
                        ℹ More Info
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Movie Modal -->
<div id="movieModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeMovieModal()">&times;</span>
        <div id="modalBody">
            <!-- Movie details will be loaded here -->
        </div>
    </div>
</div>
{% endblock %}
