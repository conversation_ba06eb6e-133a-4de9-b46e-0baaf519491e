from flask import Flask, render_template, jsonify
import json
import os

app = Flask(__name__)

# Sample movie data
MOVIES_DATA = [
    {
        "id": 1,
        "title": "The Matrix",
        "description": "A computer programmer discovers reality is a simulation.",
        "year": 1999,
        "genre": "Sci-Fi",
        "rating": "R",
        "thumbnail": "https://via.placeholder.com/300x450/000000/FFFFFF?text=The+Matrix",
        "video_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
    },
    {
        "id": 2,
        "title": "Inception",
        "description": "A thief enters people's dreams to steal secrets.",
        "year": 2010,
        "genre": "Sci-Fi",
        "rating": "PG-13",
        "thumbnail": "https://via.placeholder.com/300x450/000000/FFFFFF?text=Inception",
        "video_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4"
    },
    {
        "id": 3,
        "title": "The Dark Knight",
        "description": "Batman faces his greatest challenge yet.",
        "year": 2008,
        "genre": "Action",
        "rating": "PG-13",
        "thumbnail": "https://via.placeholder.com/300x450/000000/FFFFFF?text=Dark+Knight",
        "video_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
    },
    {
        "id": 4,
        "title": "Pulp Fiction",
        "description": "Interconnected stories of crime in Los Angeles.",
        "year": 1994,
        "genre": "Crime",
        "rating": "R",
        "thumbnail": "https://via.placeholder.com/300x450/000000/FFFFFF?text=Pulp+Fiction",
        "video_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4"
    },
    {
        "id": 5,
        "title": "Interstellar",
        "description": "A team explores space to save humanity.",
        "year": 2014,
        "genre": "Sci-Fi",
        "rating": "PG-13",
        "thumbnail": "https://via.placeholder.com/300x450/000000/FFFFFF?text=Interstellar",
        "video_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
    },
    {
        "id": 6,
        "title": "The Godfather",
        "description": "The story of a powerful crime family.",
        "year": 1972,
        "genre": "Crime",
        "rating": "R",
        "thumbnail": "https://via.placeholder.com/300x450/000000/FFFFFF?text=The+Godfather",
        "video_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4"
    }
]

@app.route('/')
def home():
    return render_template('index.html', movies=MOVIES_DATA)

@app.route('/api/movies')
def get_movies():
    return jsonify(MOVIES_DATA)

@app.route('/api/movies/<int:movie_id>')
def get_movie(movie_id):
    movie = next((m for m in MOVIES_DATA if m['id'] == movie_id), None)
    if movie:
        return jsonify(movie)
    return jsonify({'error': 'Movie not found'}), 404

@app.route('/watch/<int:movie_id>')
def watch_movie(movie_id):
    movie = next((m for m in MOVIES_DATA if m['id'] == movie_id), None)
    if movie:
        return render_template('watch.html', movie=movie)
    return "Movie not found", 404

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
