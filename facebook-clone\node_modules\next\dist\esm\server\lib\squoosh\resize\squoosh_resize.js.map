{"version": 3, "sources": ["../../../../../src/server/lib/squoosh/resize/squoosh_resize.js"], "names": ["wasm", "cachegetUint8Memory0", "getUint8Memory0", "buffer", "memory", "Uint8Array", "WASM_VECTOR_LEN", "passArray8ToWasm0", "arg", "malloc", "ptr", "length", "set", "cachegetInt32Memory0", "getInt32Memory0", "Int32Array", "cachegetUint8ClampedMemory0", "getUint8ClampedMemory0", "Uint8ClampedArray", "getClampedArrayU8FromWasm0", "len", "subarray", "resize", "input_image", "input_width", "input_height", "output_width", "output_height", "typ_idx", "premultiply", "color_space_conversion", "retptr", "__wbindgen_add_to_stack_pointer", "ptr0", "__wbindgen_malloc", "len0", "r0", "r1", "v1", "slice", "__wbindgen_free", "load", "module", "imports", "Response", "WebAssembly", "instantiateStreaming", "bytes", "arrayBuffer", "instantiate", "instance", "Instance", "init", "input", "Request", "URL", "fetch", "exports", "__wbindgen_wasm_module", "cleanup"], "mappings": "AAAA,IAAIA;AAEJ,IAAIC,uBAAuB;AAC3B,SAASC;IACP,IACED,yBAAyB,QACzBA,qBAAqBE,MAAM,KAAKH,KAAKI,MAAM,CAACD,MAAM,EAClD;QACAF,uBAAuB,IAAII,WAAWL,KAAKI,MAAM,CAACD,MAAM;IAC1D;IACA,OAAOF;AACT;AAEA,IAAIK,kBAAkB;AAEtB,SAASC,kBAAkBC,GAAG,EAAEC,MAAM;IACpC,MAAMC,MAAMD,OAAOD,IAAIG,MAAM,GAAG;IAChCT,kBAAkBU,GAAG,CAACJ,KAAKE,MAAM;IACjCJ,kBAAkBE,IAAIG,MAAM;IAC5B,OAAOD;AACT;AAEA,IAAIG,uBAAuB;AAC3B,SAASC;IACP,IACED,yBAAyB,QACzBA,qBAAqBV,MAAM,KAAKH,KAAKI,MAAM,CAACD,MAAM,EAClD;QACAU,uBAAuB,IAAIE,WAAWf,KAAKI,MAAM,CAACD,MAAM;IAC1D;IACA,OAAOU;AACT;AAEA,IAAIG,8BAA8B;AAClC,SAASC;IACP,IACED,gCAAgC,QAChCA,4BAA4Bb,MAAM,KAAKH,KAAKI,MAAM,CAACD,MAAM,EACzD;QACAa,8BAA8B,IAAIE,kBAAkBlB,KAAKI,MAAM,CAACD,MAAM;IACxE;IACA,OAAOa;AACT;AAEA,SAASG,2BAA2BT,GAAG,EAAEU,GAAG;IAC1C,OAAOH,yBAAyBI,QAAQ,CAACX,MAAM,GAAGA,MAAM,IAAIU;AAC9D;AACA;;;;;;;;;;CAUC,GACD,OAAO,SAASE,OACdC,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,OAAO,EACPC,WAAW,EACXC,sBAAsB;IAEtB,IAAI;QACF,MAAMC,SAAS/B,KAAKgC,+BAA+B,CAAC,CAAC;QACrD,IAAIC,OAAO1B,kBAAkBgB,aAAavB,KAAKkC,iBAAiB;QAChE,IAAIC,OAAO7B;QACXN,KAAKsB,MAAM,CACTS,QACAE,MACAE,MACAX,aACAC,cACAC,cACAC,eACAC,SACAC,aACAC;QAEF,IAAIM,KAAKtB,iBAAiB,CAACiB,SAAS,IAAI,EAAE;QAC1C,IAAIM,KAAKvB,iBAAiB,CAACiB,SAAS,IAAI,EAAE;QAC1C,IAAIO,KAAKnB,2BAA2BiB,IAAIC,IAAIE,KAAK;QACjDvC,KAAKwC,eAAe,CAACJ,IAAIC,KAAK;QAC9B,OAAOC;IACT,SAAU;QACRtC,KAAKgC,+BAA+B,CAAC;IACvC;AACF;AAEA,eAAeS,KAAKC,MAAM,EAAEC,OAAO;IACjC,IAAI,OAAOC,aAAa,cAAcF,kBAAkBE,UAAU;QAChE,IAAI,OAAOC,YAAYC,oBAAoB,KAAK,YAAY;YAC1D,OAAO,MAAMD,YAAYC,oBAAoB,CAACJ,QAAQC;QACxD;QAEA,MAAMI,QAAQ,MAAML,OAAOM,WAAW;QACtC,OAAO,MAAMH,YAAYI,WAAW,CAACF,OAAOJ;IAC9C,OAAO;QACL,MAAMO,WAAW,MAAML,YAAYI,WAAW,CAACP,QAAQC;QAEvD,IAAIO,oBAAoBL,YAAYM,QAAQ,EAAE;YAC5C,OAAO;gBAAED;gBAAUR;YAAO;QAC5B,OAAO;YACL,OAAOQ;QACT;IACF;AACF;AAEA,eAAeE,KAAKC,KAAK;IACvB,MAAMV,UAAU,CAAC;IAEjB,IACE,OAAOU,UAAU,YAChB,OAAOC,YAAY,cAAcD,iBAAiBC,WAClD,OAAOC,QAAQ,cAAcF,iBAAiBE,KAC/C;QACAF,QAAQG,MAAMH;IAChB;IAEA,MAAM,EAAEH,QAAQ,EAAER,MAAM,EAAE,GAAG,MAAMD,KAAK,MAAMY,OAAOV;IAErD3C,OAAOkD,SAASO,OAAO;IACvBL,KAAKM,sBAAsB,GAAGhB;IAE9B,OAAO1C;AACT;AAEA,eAAeoD,KAAI;AAEnB,+DAA+D;AAC/D,OAAO,SAASO;IACd3D,OAAO;IACPC,uBAAuB;IACvBY,uBAAuB;AACzB"}