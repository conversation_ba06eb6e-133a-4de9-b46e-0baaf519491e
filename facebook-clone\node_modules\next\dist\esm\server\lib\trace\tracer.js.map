{"version": 3, "sources": ["../../../../src/server/lib/trace/tracer.ts"], "names": ["LogSpanAllowList", "NextVanillaSpanAllowlist", "api", "process", "env", "NEXT_RUNTIME", "require", "err", "context", "propagation", "trace", "SpanStatusCode", "SpanKind", "ROOT_CONTEXT", "isPromise", "p", "then", "closeSpanWithError", "span", "error", "bubble", "setAttribute", "recordException", "setStatus", "code", "ERROR", "message", "end", "rootSpanAttributesStore", "Map", "rootSpanIdKey", "createContextKey", "lastSpanId", "getSpanId", "NextTracerImpl", "getTracerInstance", "getTracer", "getContext", "getActiveScopeSpan", "getSpan", "active", "withPropagatedContext", "carrier", "fn", "getter", "activeContext", "getSpanContext", "remoteContext", "extract", "with", "args", "type", "fnOrOptions", "fnOrEmpty", "options", "spanName", "includes", "NEXT_OTEL_VERBOSE", "hideSpan", "spanContext", "parentSpan", "isRootSpan", "isRemote", "spanId", "attributes", "setValue", "startActiveSpan", "startTime", "globalThis", "performance", "now", "undefined", "onCleanup", "delete", "NEXT_OTEL_PERFORMANCE_PREFIX", "measure", "split", "pop", "replace", "match", "toLowerCase", "start", "set", "Object", "entries", "length", "result", "res", "catch", "finally", "wrap", "tracer", "name", "optionsObj", "apply", "arguments", "lastArgId", "cb", "scopeBoundCb", "bind", "_span", "done", "startSpan", "setSpan", "getRootSpanAttributes", "getValue", "get"], "mappings": "AACA,SAASA,gBAAgB,EAAEC,wBAAwB,QAAQ,cAAa;AAWxE,IAAIC;AAEJ,gFAAgF;AAChF,8EAA8E;AAC9E,uCAAuC;AACvC,0EAA0E;AAC1E,+EAA+E;AAC/E,4CAA4C;AAC5C,6CAA6C;AAC7C,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCH,MAAMI,QAAQ;AAChB,OAAO;IACL,IAAI;QACFJ,MAAMI,QAAQ;IAChB,EAAE,OAAOC,KAAK;QACZL,MAAMI,QAAQ;IAChB;AACF;AAEA,MAAM,EAAEE,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAC3EX;AAEF,MAAMY,YAAY,CAAIC;IACpB,OAAOA,MAAM,QAAQ,OAAOA,MAAM,YAAY,OAAOA,EAAEC,IAAI,KAAK;AAClE;AAIA,MAAMC,qBAAqB,CAACC,MAAYC;IACtC,IAAI,CAACA,yBAAD,AAACA,MAAoCC,MAAM,MAAK,MAAM;QACxDF,KAAKG,YAAY,CAAC,eAAe;IACnC,OAAO;QACL,IAAIF,OAAO;YACTD,KAAKI,eAAe,CAACH;QACvB;QACAD,KAAKK,SAAS,CAAC;YAAEC,MAAMb,eAAec,KAAK;YAAEC,OAAO,EAAEP,yBAAAA,MAAOO,OAAO;QAAC;IACvE;IACAR,KAAKS,GAAG;AACV;AAqGA,8EAA8E,GAC9E,MAAMC,0BAA0B,IAAIC;AAIpC,MAAMC,gBAAgB5B,IAAI6B,gBAAgB,CAAC;AAC3C,IAAIC,aAAa;AACjB,MAAMC,YAAY,IAAMD;AAExB,MAAME;IACJ;;;;GAIC,GACD,AAAQC,oBAA4B;QAClC,OAAOzB,MAAM0B,SAAS,CAAC,WAAW;IACpC;IAEOC,aAAyB;QAC9B,OAAO7B;IACT;IAEO8B,qBAAuC;QAC5C,OAAO5B,MAAM6B,OAAO,CAAC/B,2BAAAA,QAASgC,MAAM;IACtC;IAEOC,sBACLC,OAAU,EACVC,EAAW,EACXC,MAAyB,EACtB;QACH,MAAMC,gBAAgBrC,QAAQgC,MAAM;QACpC,IAAI9B,MAAMoC,cAAc,CAACD,gBAAgB;YACvC,qDAAqD;YACrD,OAAOF;QACT;QACA,MAAMI,gBAAgBtC,YAAYuC,OAAO,CAACH,eAAeH,SAASE;QAClE,OAAOpC,QAAQyC,IAAI,CAACF,eAAeJ;IACrC;IAsBOjC,MAAS,GAAGwC,IAAgB,EAAE;YAwCxBxC;QAvCX,MAAM,CAACyC,MAAMC,aAAaC,UAAU,GAAGH;QAEvC,+BAA+B;QAC/B,MAAM,EACJP,EAAE,EACFW,OAAO,EACR,GAIC,OAAOF,gBAAgB,aACnB;YACET,IAAIS;YACJE,SAAS,CAAC;QACZ,IACA;YACEX,IAAIU;YACJC,SAAS;gBAAE,GAAGF,WAAW;YAAC;QAC5B;QAEN,MAAMG,WAAWD,QAAQC,QAAQ,IAAIJ;QAErC,IACE,AAAC,CAAClD,yBAAyBuD,QAAQ,CAACL,SAClChD,QAAQC,GAAG,CAACqD,iBAAiB,KAAK,OACpCH,QAAQI,QAAQ,EAChB;YACA,OAAOf;QACT;QAEA,mHAAmH;QACnH,IAAIgB,cAAc,IAAI,CAACb,cAAc,CACnCQ,CAAAA,2BAAAA,QAASM,UAAU,KAAI,IAAI,CAACtB,kBAAkB;QAEhD,IAAIuB,aAAa;QAEjB,IAAI,CAACF,aAAa;YAChBA,cAAcnD,CAAAA,2BAAAA,QAASgC,MAAM,OAAM3B;YACnCgD,aAAa;QACf,OAAO,KAAInD,wBAAAA,MAAMoC,cAAc,CAACa,iCAArBjD,sBAAmCoD,QAAQ,EAAE;YACtDD,aAAa;QACf;QAEA,MAAME,SAAS9B;QAEfqB,QAAQU,UAAU,GAAG;YACnB,kBAAkBT;YAClB,kBAAkBJ;YAClB,GAAGG,QAAQU,UAAU;QACvB;QAEA,OAAOxD,QAAQyC,IAAI,CAACU,YAAYM,QAAQ,CAACnC,eAAeiC,SAAS,IAC/D,IAAI,CAAC5B,iBAAiB,GAAG+B,eAAe,CACtCX,UACAD,SACA,CAACpC;gBACC,MAAMiD,YACJ,iBAAiBC,aACbA,WAAWC,WAAW,CAACC,GAAG,KAC1BC;gBAEN,MAAMC,YAAY;oBAChB5C,wBAAwB6C,MAAM,CAACV;oBAC/B,IACEI,aACAhE,QAAQC,GAAG,CAACsE,4BAA4B,IACxC1E,iBAAiBwD,QAAQ,CAACL,QAAS,KACnC;wBACAkB,YAAYM,OAAO,CACjB,CAAC,EAAExE,QAAQC,GAAG,CAACsE,4BAA4B,CAAC,MAAM,EAAE,AAClDvB,CAAAA,KAAKyB,KAAK,CAAC,KAAKC,GAAG,MAAM,EAAC,EAC1BC,OAAO,CACP,UACA,CAACC,QAAkB,MAAMA,MAAMC,WAAW,IAC1C,CAAC,EACH;4BACEC,OAAOd;4BACPxC,KAAK0C,YAAYC,GAAG;wBACtB;oBAEJ;gBACF;gBAEA,IAAIT,YAAY;oBACdjC,wBAAwBsD,GAAG,CACzBnB,QACA,IAAIlC,IACFsD,OAAOC,OAAO,CAAC9B,QAAQU,UAAU,IAAI,CAAC;gBAM5C;gBACA,IAAI;oBACF,IAAIrB,GAAG0C,MAAM,GAAG,GAAG;wBACjB,OAAO1C,GAAGzB,MAAM,CAACX,MAAgBU,mBAAmBC,MAAMX;oBAC5D;oBAEA,MAAM+E,SAAS3C,GAAGzB;oBAClB,IAAIJ,UAAUwE,SAAS;wBACrB,uCAAuC;wBACvC,OAAOA,OACJtE,IAAI,CAAC,CAACuE;4BACLrE,KAAKS,GAAG;4BACR,wCAAwC;4BACxC,iEAAiE;4BACjE,OAAO4D;wBACT,GACCC,KAAK,CAAC,CAACjF;4BACNU,mBAAmBC,MAAMX;4BACzB,MAAMA;wBACR,GACCkF,OAAO,CAACjB;oBACb,OAAO;wBACLtD,KAAKS,GAAG;wBACR6C;oBACF;oBAEA,OAAOc;gBACT,EAAE,OAAO/E,KAAU;oBACjBU,mBAAmBC,MAAMX;oBACzBiE;oBACA,MAAMjE;gBACR;YACF;IAGN;IAaOmF,KAAK,GAAGxC,IAAgB,EAAE;QAC/B,MAAMyC,SAAS,IAAI;QACnB,MAAM,CAACC,MAAMtC,SAASX,GAAG,GACvBO,KAAKmC,MAAM,KAAK,IAAInC,OAAO;YAACA,IAAI,CAAC,EAAE;YAAE,CAAC;YAAGA,IAAI,CAAC,EAAE;SAAC;QAEnD,IACE,CAACjD,yBAAyBuD,QAAQ,CAACoC,SACnCzF,QAAQC,GAAG,CAACqD,iBAAiB,KAAK,KAClC;YACA,OAAOd;QACT;QAEA,OAAO;YACL,IAAIkD,aAAavC;YACjB,IAAI,OAAOuC,eAAe,cAAc,OAAOlD,OAAO,YAAY;gBAChEkD,aAAaA,WAAWC,KAAK,CAAC,IAAI,EAAEC;YACtC;YAEA,MAAMC,YAAYD,UAAUV,MAAM,GAAG;YACrC,MAAMY,KAAKF,SAAS,CAACC,UAAU;YAE/B,IAAI,OAAOC,OAAO,YAAY;gBAC5B,MAAMC,eAAeP,OAAOtD,UAAU,GAAG8D,IAAI,CAAC3F,QAAQgC,MAAM,IAAIyD;gBAChE,OAAON,OAAOjF,KAAK,CAACkF,MAAMC,YAAY,CAACO,OAAOC;oBAC5CN,SAAS,CAACC,UAAU,GAAG,SAAUzF,GAAQ;wBACvC8F,wBAAAA,KAAO9F;wBACP,OAAO2F,aAAaJ,KAAK,CAAC,IAAI,EAAEC;oBAClC;oBAEA,OAAOpD,GAAGmD,KAAK,CAAC,IAAI,EAAEC;gBACxB;YACF,OAAO;gBACL,OAAOJ,OAAOjF,KAAK,CAACkF,MAAMC,YAAY,IAAMlD,GAAGmD,KAAK,CAAC,IAAI,EAAEC;YAC7D;QACF;IACF;IAIOO,UAAU,GAAGpD,IAAgB,EAAQ;QAC1C,MAAM,CAACC,MAAMG,QAAQ,GAA4CJ;QAEjE,MAAMS,cAAc,IAAI,CAACb,cAAc,CACrCQ,CAAAA,2BAAAA,QAASM,UAAU,KAAI,IAAI,CAACtB,kBAAkB;QAEhD,OAAO,IAAI,CAACH,iBAAiB,GAAGmE,SAAS,CAACnD,MAAMG,SAASK;IAC3D;IAEQb,eAAec,UAAiB,EAAE;QACxC,MAAMD,cAAcC,aAChBlD,MAAM6F,OAAO,CAAC/F,QAAQgC,MAAM,IAAIoB,cAChCW;QAEJ,OAAOZ;IACT;IAEO6C,wBAAwB;QAC7B,MAAMzC,SAASvD,QAAQgC,MAAM,GAAGiE,QAAQ,CAAC3E;QACzC,OAAOF,wBAAwB8E,GAAG,CAAC3C;IACrC;AACF;AAEA,MAAM3B,YAAY,AAAC,CAAA;IACjB,MAAMuD,SAAS,IAAIzD;IAEnB,OAAO,IAAMyD;AACf,CAAA;AAEA,SAASvD,SAAS,EAAEzB,cAAc,EAAEC,QAAQ,GAAE"}