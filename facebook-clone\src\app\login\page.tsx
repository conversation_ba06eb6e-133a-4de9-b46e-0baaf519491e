'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

export default function Login() {
  const router = useRouter()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    // Simulate authentication
    setTimeout(() => {
      // For demo purposes, accept any email with a password longer than 5 chars
      if (email.includes('@') && password.length > 5) {
        // Store user in localStorage (in a real app, you'd use cookies/JWT)
        const user = {
          id: 1,
          name: email.split('@')[0].replace(/[^a-zA-Z0-9]/g, ' '),
          email,
          avatar: `https://via.placeholder.com/40/1877f2/ffffff?text=${email.substring(0, 2).toUpperCase()}`
        }
        localStorage.setItem('facebookUser', JSON.stringify(user))
        router.push('/')
      } else {
        setError('Invalid email or password')
      }
      setIsLoading(false)
    }, 1000)
  }
  
  const handleCreateAccount = () => {
    router.push('/register')
  }

  return (
    <div className="min-h-screen bg-facebook-gray flex flex-col items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="text-center mb-8">
          <h1 className="text-facebook-blue text-5xl font-bold mb-2">facebook</h1>
          <p className="text-xl text-facebook-text-dark">
            Connect with friends and the world around you on Facebook.
          </p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-md">
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg">
              {error}
            </div>
          )}

          <form onSubmit={handleLogin} className="space-y-4">
            <div>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email address"
                required
                className="facebook-input"
              />
            </div>

            <div>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Password"
                required
                className="facebook-input"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="facebook-button w-full py-3 text-xl"
            >
              {isLoading ? 'Logging in...' : 'Log In'}
            </button>

            <div className="text-center">
              <Link href="#" className="text-facebook-blue hover:underline text-sm">
                Forgotten password?
              </Link>
            </div>

            <div className="border-t border-gray-200 pt-4">
              <button
                type="button"
                onClick={handleCreateAccount}
                className="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 w-3/5 mx-auto block"
              >
                Create New Account
              </button>
            </div>
          </form>
        </div>

        <div className="text-center mt-6 text-sm">
          <p>
            <Link href="#" className="font-bold hover:underline">
              Create a Page
            </Link>{' '}
            for a celebrity, brand or business.
          </p>
        </div>
      </div>
    </div>
  )
}