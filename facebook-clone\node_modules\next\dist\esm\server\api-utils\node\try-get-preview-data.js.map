{"version": 3, "sources": ["../../../../src/server/api-utils/node/try-get-preview-data.ts"], "names": ["checkIsOnDemandRevalidate", "clearPreviewData", "COOKIE_NAME_PRERENDER_BYPASS", "COOKIE_NAME_PRERENDER_DATA", "SYMBOL_PREVIEW_DATA", "RequestCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tryGetPreviewData", "req", "res", "options", "cookies", "isOnDemandRevalidate", "headers", "from", "previewModeId", "get", "value", "tokenPreviewData", "data", "Object", "defineProperty", "enumerable", "encryptedPreviewData", "jsonwebtoken", "require", "verify", "previewModeSigningKey", "decryptWithSecret", "decryptedPreviewData", "<PERSON><PERSON><PERSON>", "previewModeEncryptionKey", "JSON", "parse"], "mappings": "AAEA,SAASA,yBAAyB,QAAQ,OAAM;AAKhD,SACEC,gBAAgB,EAChBC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,mBAAmB,QACd,WAAU;AACjB,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,cAAc,QAAQ,4CAA2C;AAE1E,OAAO,SAASC,kBACdC,GAAgD,EAChDC,GAAsC,EACtCC,OAA0B;QAiBJC,cACGA;IAhBzB,0DAA0D;IAC1D,cAAc;IACd,IAAID,WAAWV,0BAA0BQ,KAAKE,SAASE,oBAAoB,EAAE;QAC3E,OAAO;IACT;IAEA,sCAAsC;IACtC,iDAAiD;IACjD,IAAIR,uBAAuBI,KAAK;QAC9B,OAAO,AAACA,GAAW,CAACJ,oBAAoB;IAC1C;IAEA,MAAMS,UAAUP,eAAeQ,IAAI,CAACN,IAAIK,OAAO;IAC/C,MAAMF,UAAU,IAAIN,eAAeQ;IAEnC,MAAME,iBAAgBJ,eAAAA,QAAQK,GAAG,CAACd,kDAAZS,aAA2CM,KAAK;IACtE,MAAMC,oBAAmBP,gBAAAA,QAAQK,GAAG,CAACb,gDAAZQ,cAAyCM,KAAK;IAEvE,2DAA2D;IAC3D,IACEF,iBACA,CAACG,oBACDH,kBAAkBL,QAAQK,aAAa,EACvC;QACA,yCAAyC;QACzC,4CAA4C;QAC5C,4CAA4C;QAC5C,MAAMI,OAAO,CAAC;QACdC,OAAOC,cAAc,CAACb,KAAKJ,qBAAqB;YAC9Ca,OAAOE;YACPG,YAAY;QACd;QACA,OAAOH;IACT;IAEA,+BAA+B;IAC/B,IAAI,CAACJ,iBAAiB,CAACG,kBAAkB;QACvC,OAAO;IACT;IAEA,8CAA8C;IAC9C,IAAI,CAACH,iBAAiB,CAACG,kBAAkB;QACvCjB,iBAAiBQ;QACjB,OAAO;IACT;IAEA,6CAA6C;IAC7C,IAAIM,kBAAkBL,QAAQK,aAAa,EAAE;QAC3Cd,iBAAiBQ;QACjB,OAAO;IACT;IAEA,IAAIc;IAGJ,IAAI;QACF,MAAMC,eACJC,QAAQ;QACVF,uBAAuBC,aAAaE,MAAM,CACxCR,kBACAR,QAAQiB,qBAAqB;IAEjC,EAAE,OAAM;QACN,aAAa;QACb1B,iBAAiBQ;QACjB,OAAO;IACT;IAEA,MAAM,EAAEmB,iBAAiB,EAAE,GACzBH,QAAQ;IACV,MAAMI,uBAAuBD,kBAC3BE,OAAOhB,IAAI,CAACJ,QAAQqB,wBAAwB,GAC5CR,qBAAqBJ,IAAI;IAG3B,IAAI;QACF,qCAAqC;QACrC,MAAMA,OAAOa,KAAKC,KAAK,CAACJ;QACxB,eAAe;QACfT,OAAOC,cAAc,CAACb,KAAKJ,qBAAqB;YAC9Ca,OAAOE;YACPG,YAAY;QACd;QACA,OAAOH;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF"}