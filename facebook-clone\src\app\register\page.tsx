'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

export default function Register() {
  const router = useRouter()
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [birthDay, setBirthDay] = useState('')
  const [birthMonth, setBirthMonth] = useState('')
  const [birthYear, setBirthYear] = useState('')
  const [gender, setGender] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleRegister = (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    // Basic validation
    if (password !== confirmPassword) {
      setError('Passwords do not match')
      setIsLoading(false)
      return
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters')
      setIsLoading(false)
      return
    }

    // Simulate registration
    setTimeout(() => {
      // Create user object
      const user = {
        id: 1,
        name: `${firstName} ${lastName}`,
        email,
        avatar: `https://via.placeholder.com/40/1877f2/ffffff?text=${firstName.charAt(0)}${lastName.charAt(0)}`
      }
      
      // Store user in localStorage (in a real app, you'd use cookies/JWT)
      localStorage.setItem('facebookUser', JSON.stringify(user))
      router.push('/')
      
      setIsLoading(false)
    }, 1000)
  }

  // Generate options for birth date selects
  const days = Array.from({ length: 31 }, (_, i) => i + 1)
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 100 }, (_, i) => currentYear - i)

  return (
    <div className="min-h-screen bg-facebook-gray flex flex-col items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="text-center mb-8">
          <h1 className="text-facebook-blue text-5xl font-bold mb-2">facebook</h1>
          <p className="text-xl text-facebook-text-dark">
            Create a new account
          </p>
          <p className="text-sm text-facebook-text-gray mt-1">
            It's quick and easy.
          </p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-md">
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg">
              {error}
            </div>
          )}

          <form onSubmit={handleRegister} className="space-y-4">
            <div className="flex space-x-2">
              <input
                type="text"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                placeholder="First name"
                required
                className="facebook-input flex-1"
              />
              <input
                type="text"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                placeholder="Last name"
                required
                className="facebook-input flex-1"
              />
            </div>

            <div>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email address"
                required
                className="facebook-input"
              />
            </div>

            <div>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="New password"
                required
                className="facebook-input"
              />
            </div>

            <div>
              <input
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirm password"
                required
                className="facebook-input"
              />
            </div>

            <div>
              <label className="block text-sm text-facebook-text-gray mb-1">
                Birthday
              </label>
              <div className="flex space-x-2">
                <select
                  value={birthDay}
                  onChange={(e) => setBirthDay(e.target.value)}
                  className="facebook-input flex-1"
                  required
                >
                  <option value="">Day</option>
                  {days.map(day => (
                    <option key={day} value={day}>
                      {day}
                    </option>
                  ))}
                </select>
                <select
                  value={birthMonth}
                  onChange={(e) => setBirthMonth(e.target.value)}
                  className="facebook-input flex-1"
                  required
                >
                  <option value="">Month</option>
                  {months.map((month, index) => (
                    <option key={month} value={index + 1}>
                      {month}
                    </option>
                  ))}
                </select>
                <select
                  value={birthYear}
                  onChange={(e) => setBirthYear(e.target.value)}
                  className="facebook-input flex-1"
                  required
                >
                  <option value="">Year</option>
                  {years.map(year => (
                    <option key={year} value={year}>
                      {year}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm text-facebook-text-gray mb-1">
                Gender
              </label>
              <div className="flex space-x-2">
                <label className="flex-1 border border-gray-300 rounded-lg p-2 flex items-center justify-between">
                  <span>Female</span>
                  <input
                    type="radio"
                    name="gender"
                    value="female"
                    checked={gender === 'female'}
                    onChange={() => setGender('female')}
                    required
                  />
                </label>
                <label className="flex-1 border border-gray-300 rounded-lg p-2 flex items-center justify-between">
                  <span>Male</span>
                  <input
                    type="radio"
                    name="gender"
                    value="male"
                    checked={gender === 'male'}
                    onChange={() => setGender('male')}
                  />
                </label>
                <label className="flex-1 border border-gray-300 rounded-lg p-2 flex items-center justify-between">
                  <span>Custom</span>
                  <input
                    type="radio"
                    name="gender"
                    value="custom"
                    checked={gender === 'custom'}
                    onChange={() => setGender('custom')}
                  />
                </label>
              </div>
            </div>

            <div className="text-xs text-facebook-text-gray">
              By clicking Sign Up, you agree to our Terms, Privacy Policy and Cookies Policy.
            </div>

            <div className="flex justify-center">
              <button
                type="submit"
                disabled={isLoading}
                className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-8 rounded-lg transition-colors duration-200"
              >
                {isLoading ? 'Signing Up...' : 'Sign Up'}
              </button>
            </div>

            <div className="text-center">
              <Link href="/login" className="text-facebook-blue hover:underline">
                Already have an account?
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}