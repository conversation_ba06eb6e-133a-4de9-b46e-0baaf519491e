#
# Python Version: 3.13 or higher
#
# Dependencies: This script requires 'requests' and 'beautifulsoup4'.
# To install them, run the following command in your terminal:
# pip install requests beautifulsoup4
#
# ---
#
# Usage:
# The script can automatically generate the URL for standard Microsoft Defender XDR tables.
#
#   # To generate commands for a standard table like 'EmailEvents':
#   python adx_schema_generator.py -t EmailEvents
#
#   # For a table with a non-standard URL, use the -u flag to override auto-generation:
#   python adx_schema_generator.py -t MyCustomTable -u https://example.com/custom-schema-page
#
# Created by <PERSON>
# ---

import requests
from bs4 import BeautifulSoup
import argparse
import sys

# --- Configuration ---
BASE_URL_TEMPLATE = "https://learn.microsoft.com/en-us/defender-xdr/advanced-hunting-{table_name_lower}-table"

WEB_TO_ADX_TYPE_MAP = {
    'boolean': 'bool',
    'bool': 'bool',
    'string': 'string',
    'datetime': 'datetime',
    'long': 'long',
    'dynamic': 'dynamic',
    'int': 'int'
}

ADX_TYPE_TO_KQL_CAST_MAP = {
    'bool': 'tobool',
    'string': 'tostring',
    'datetime': 'todatetime',
    'long': 'tolong',
    'dynamic': 'todynamic',
    'int': 'toint'
}

def scrape_schema_from_url(url: str) -> list[tuple[str, str]]:
    """
    Scrapes a Microsoft Learn page to find the table schema.
    """
    print(f"[*] Scraping schema from: {url}")
    try:
        response = requests.get(url, timeout=15)
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        if hasattr(e, 'response') and e.response is not None and e.response.status_code == 404:
             print(f"[!] Error: The URL returned a 404 Not Found error. The table name might be incorrect or doesn't follow the standard URL pattern.", file=sys.stderr)
             print(f"[!] Tip: Try finding the correct URL manually and provide it using the -u flag.", file=sys.stderr)
        else:
            print(f"[!] Error: Could not fetch the URL. {e}", file=sys.stderr)
        return []

    soup = BeautifulSoup(response.text, 'html.parser')
    
    main_content = soup.find('main', id='main')
    if not main_content:
        print("[!] Error: Could not find the main content area of the page.", file=sys.stderr)
        return []

    table = main_content.find('table')
    if not table:
        print("[!] Error: Could not find a schema table on the page.", file=sys.stderr)
        return []

    schema = []
    for row in table.find('tbody').find_all('tr'):
        cells = row.find_all('td')
        if len(cells) >= 2:
            column_name = cells[0].get_text(strip=True)
            data_type = cells[1].get_text(strip=True).lower()
            schema.append((column_name, data_type))
    
    if not schema:
        print("[!] Warning: Found a table but could not extract any schema rows.", file=sys.stderr)
    else:
        print(f"[*] Successfully extracted {len(schema)} columns.")
        
    return schema

def generate_create_table_command(schema: list, table_name: str) -> str:
    """Generates the .create table KQL command."""
    columns_definitions = [f"{col_name}:{WEB_TO_ADX_TYPE_MAP.get(web_type, 'string')}" for col_name, web_type in schema]
    columns_string = ", ".join(columns_definitions)
    return f".create table {table_name} ({columns_string})"

def generate_function_command(schema: list, table_name: str) -> str:
    """Generates the .create-or-alter function for the update policy."""
    raw_table_name = f"{table_name}Raw"
    function_name = f"{table_name}Expand"
    
    projections = []
    for col_name, web_type in schema:
        adx_type = WEB_TO_ADX_TYPE_MAP.get(web_type, 'string')
        cast_function = ADX_TYPE_TO_KQL_CAST_MAP.get(adx_type, 'tostring')
        projections.append(f"{col_name} = {cast_function}(events.properties.{col_name})")

    projections_string = ",\n    ".join(projections)

    kql_body = f"""{raw_table_name}
| mv-expand events = records
| project {projections_string}"""
    
    return f""".create-or-alter function {function_name} {{
{kql_body}
}}"""

def generate_update_policy_command(table_name: str) -> str:
    """Generates the .alter table policy update command."""
    raw_table_name = f"{table_name}Raw"
    function_name = f"{table_name}Expand()"
    policy_json = f'[{{"Source": "{raw_table_name}", "Query": "{function_name}", "IsEnabled": "True", "IsTransactional": "true"}}]'
    return f".alter table {table_name} policy update @'{policy_json}'"

def main():
    """Main function to parse arguments and run the script."""
    parser = argparse.ArgumentParser(
        description="Generate ADX KQL commands by scraping a Microsoft Defender XDR schema page.",
        formatter_class=argparse.RawTextHelpFormatter,
        epilog="""Examples:
  # Generate commands for the 'DeviceInfo' table (uses default)
  python %(prog)s

  # Generate commands for the 'EmailEvents' table by auto-generating the URL
  python %(prog)s -t EmailEvents

  # Override URL generation for a non-standard URL
  python %(prog)s -t MyCustomTable -u https://example.com/my-custom-schema-page"""
    )
    parser.add_argument(
        '-t', '--table-name',
        default='DeviceInfo',
        help='The base name for the ADX tables (e.g., "DeviceInfo").\nThis is also used to auto-generate the URL unless -u is specified.'
    )
    parser.add_argument(
        '-u', '--url',
        default=None,
        help='Full URL of the schema page. Overrides automatic URL generation.'
    )
    args = parser.parse_args()

    # --- New URL Generation Logic ---
    if args.url is None:
        table_name_for_url = args.table_name.lower()
        generated_url = BASE_URL_TEMPLATE.format(table_name_lower=table_name_for_url)
        print(f"[*] No custom URL provided. Auto-generating from table name: '{args.table_name}'")
        args.url = generated_url
    else:
        print(f"[*] Custom URL provided. Overriding auto-generation.")

    schema = scrape_schema_from_url(args.url)
    if not schema:
        sys.exit(1)

    # --- Generate and Print Commands ---
    cmd_create_table = generate_create_table_command(schema, args.table_name)
    cmd_create_function = generate_function_command(schema, args.table_name)
    cmd_update_policy = generate_update_policy_command(args.table_name)

    print("\n" + "="*80)
    print(f"Azure Data Explorer (KQL) Commands for Table: {args.table_name}")
    print("="*80 + "\n")
    print("--- 1. Command to Create Destination Table ---")
    print(cmd_create_table)
    print("\n" + "-"*80 + "\n")
    print("--- 2. Command to Create Transformation Function ---")
    print(cmd_create_function)
    print("\n" + "-"*80 + "\n")
    print("--- 3. Command to Set the Update Policy on the Table ---")
    print(cmd_update_policy)
    print("\n" + "="*80)

if __name__ == '__main__':
    main()