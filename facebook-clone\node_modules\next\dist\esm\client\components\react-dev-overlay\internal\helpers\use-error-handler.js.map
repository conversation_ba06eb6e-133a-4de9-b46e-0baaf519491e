{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/use-error-handler.ts"], "names": ["useEffect", "hydrationErrorState", "isNextRouterError", "isHydrationError", "window", "Error", "stackTraceLimit", "hasHydrationError", "errorQueue", "rejectionQueue", "errorHandlers", "rejectionHandlers", "addEventListener", "ev", "error", "preventDefault", "stack", "isCausedByHydrationFailure", "message", "includes", "warning", "details", "e", "push", "handler", "reason", "useErrorHandler", "handleOnUnhandledError", "handleOnUnhandledRejection", "for<PERSON>ach", "splice", "indexOf"], "mappings": "AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SAASC,mBAAmB,QAAQ,yBAAwB;AAC5D,SAASC,iBAAiB,QAAQ,gCAA+B;AACjE,SAASC,gBAAgB,QAAQ,8BAA6B;AAI9D,IAAI,OAAOC,WAAW,aAAa;IACjC,IAAI;QACF,oDAAoD;QACpDC,MAAMC,eAAe,GAAG;IAC1B,EAAE,UAAM,CAAC;AACX;AAEA,IAAIC,oBAAoB;AACxB,MAAMC,aAA2B,EAAE;AACnC,MAAMC,iBAA+B,EAAE;AACvC,MAAMC,gBAAqC,EAAE;AAC7C,MAAMC,oBAAyC,EAAE;AAEjD,IAAI,OAAOP,WAAW,aAAa;IACjC,6EAA6E;IAC7E,0EAA0E;IAC1E,yBAAyB;IACzBA,OAAOQ,gBAAgB,CAAC,SAAS,CAACC;QAChC,IAAIX,kBAAkBW,GAAGC,KAAK,GAAG;YAC/BD,GAAGE,cAAc;YACjB;QACF;QAEA,MAAMD,QAAQD,sBAAAA,GAAIC,KAAK;QACvB,IACE,CAACA,SACD,CAAEA,CAAAA,iBAAiBT,KAAI,KACvB,OAAOS,MAAME,KAAK,KAAK,UACvB;YACA,8DAA8D;YAC9D;QACF;QAEA,MAAMC,6BAA6Bd,iBAAiBW;QACpD,IACEX,iBAAiBW,UACjB,CAACA,MAAMI,OAAO,CAACC,QAAQ,CACrB,2DAEF;YACA,oEAAoE;YACpE,kDAAkD;YAClD,IAAIlB,oBAAoBmB,OAAO,EAAE;gBAG7BN,MAAcO,OAAO,GAAG;oBACxB,GAAG,AAACP,MAAcO,OAAO;oBACzB,wEAAwE;oBACxE,GAAGpB,mBAAmB;gBACxB;YACF;YACAa,MAAMI,OAAO,IACX;QACJ;QAEA,MAAMI,IAAIR;QACV,sCAAsC;QACtC,IAAIG,4BAA4B;YAC9B,IAAI,CAACV,mBAAmB;gBACtBC,WAAWe,IAAI,CAACD;YAClB;YACAf,oBAAoB;QACtB;QACA,KAAK,MAAMiB,WAAWd,cAAe;YACnCc,QAAQF;QACV;IACF;IACAlB,OAAOQ,gBAAgB,CACrB,sBACA,CAACC;QACC,MAAMY,SAASZ,sBAAAA,GAAIY,MAAM;QACzB,IACE,CAACA,UACD,CAAEA,CAAAA,kBAAkBpB,KAAI,KACxB,OAAOoB,OAAOT,KAAK,KAAK,UACxB;YACA,8DAA8D;YAC9D;QACF;QAEA,MAAMM,IAAIG;QACVhB,eAAec,IAAI,CAACD;QACpB,KAAK,MAAME,WAAWb,kBAAmB;YACvCa,QAAQF;QACV;IACF;AAEJ;AAEA,OAAO,SAASI,gBACdC,sBAAoC,EACpCC,0BAAwC;IAExC5B,UAAU;QACR,wBAAwB;QACxBQ,WAAWqB,OAAO,CAACF;QACnBlB,eAAeoB,OAAO,CAACD;QAEvB,wBAAwB;QACxBlB,cAAca,IAAI,CAACI;QACnBhB,kBAAkBY,IAAI,CAACK;QAEvB,OAAO;YACL,oBAAoB;YACpBlB,cAAcoB,MAAM,CAACpB,cAAcqB,OAAO,CAACJ,yBAAyB;YACpEhB,kBAAkBmB,MAAM,CACtBnB,kBAAkBoB,OAAO,CAACH,6BAC1B;QAEJ;IACF,GAAG;QAACD;QAAwBC;KAA2B;AACzD"}