'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Header from '@/components/Header'
import { Camera, Edit, MapPin, Briefcase, GraduationCap, Heart } from 'lucide-react'
import Post from '@/components/Post'

interface User {
  id: number
  name: string
  avatar: string
  email: string
}

interface PostData {
  id: number
  user: {
    name: string
    avatar: string
  }
  content: string
  image?: string
  timestamp: string
  likes: number
  comments: number
  shares: number
  isLiked: boolean
}

export default function Profile() {
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [userPosts, setUserPosts] = useState<PostData[]>([])
  const [activeTab, setActiveTab] = useState('posts')

  useEffect(() => {
    // Check if user is logged in
    const storedUser = localStorage.getItem('facebookUser')
    if (!storedUser) {
      router.push('/login')
      return
    }

    setUser(JSON.parse(storedUser))
    setLoading(false)

    // Generate some posts for the user
    const posts = [
      {
        id: 1,
        user: {
          name: JSON.parse(storedUser).name,
          avatar: JSON.parse(storedUser).avatar
        },
        content: 'Just updated my profile on Facebook! Excited to connect with everyone here.',
        timestamp: '2 days ago',
        likes: 15,
        comments: 3,
        shares: 1,
        isLiked: false
      },
      {
        id: 2,
        user: {
          name: JSON.parse(storedUser).name,
          avatar: JSON.parse(storedUser).avatar
        },
        content: 'Beautiful day for a hike! Nature is truly amazing. 🌲🏞️',
        image: 'https://images.unsplash.com/photo-1501854140801-50d01698950b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
        timestamp: '1 week ago',
        likes: 42,
        comments: 7,
        shares: 3,
        isLiked: true
      }
    ]
    setUserPosts(posts)
  }, [router])

  const handleLike = (postId: number) => {
    setUserPosts(userPosts.map(post => 
      post.id === postId 
        ? { 
            ...post, 
            isLiked: !post.isLiked,
            likes: post.isLiked ? post.likes - 1 : post.likes + 1
          }
        : post
    ))
  }

  if (loading || !user) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>
  }

  return (
    <div className="min-h-screen bg-facebook-gray">
      <Header user={user} />
      
      <div className="pt-16">
        {/* Cover Photo */}
        <div className="relative h-80 bg-gradient-to-r from-blue-400 to-blue-600">
          <button className="absolute bottom-4 right-4 bg-white p-2 rounded-full shadow-md">
            <Camera className="w-5 h-5 text-facebook-text-dark" />
          </button>
        </div>
        
        {/* Profile Info */}
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="flex flex-col sm:flex-row items-center sm:items-end -mt-16 sm:-mt-24 mb-4 sm:mb-0">
            <div className="relative">
              <img
                src={user.avatar}
                alt={user.name}
                className="w-32 h-32 sm:w-40 sm:h-40 rounded-full border-4 border-white"
              />
              <button className="absolute bottom-2 right-2 bg-facebook-gray p-2 rounded-full shadow-md">
                <Camera className="w-5 h-5 text-facebook-text-dark" />
              </button>
            </div>
            
            <div className="mt-4 sm:mt-0 sm:ml-6 text-center sm:text-left flex-1">
              <h1 className="text-3xl font-bold text-facebook-text-dark">{user.name}</h1>
              <p className="text-facebook-text-gray">120 friends</p>
              <div className="flex mt-2 space-x-2 justify-center sm:justify-start">
                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                  <img
                    src="https://via.placeholder.com/20/ffffff/ffffff?text=F"
                    alt="Friend"
                    className="rounded-full"
                  />
                </div>
                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                  <img
                    src="https://via.placeholder.com/20/ffffff/ffffff?text=F"
                    alt="Friend"
                    className="rounded-full"
                  />
                </div>
                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                  <img
                    src="https://via.placeholder.com/20/ffffff/ffffff?text=F"
                    alt="Friend"
                    className="rounded-full"
                  />
                </div>
              </div>
            </div>
            
            <div className="mt-4 sm:mt-0 flex space-x-2">
              <button className="facebook-button flex items-center space-x-1">
                <Edit className="w-4 h-4" />
                <span>Edit Profile</span>
              </button>
            </div>
          </div>
          
          <div className="border-t border-gray-300 mt-4 pt-1">
            <div className="flex overflow-x-auto">
              <button 
                onClick={() => setActiveTab('posts')}
                className={`px-4 py-3 font-medium ${
                  activeTab === 'posts' 
                    ? 'text-facebook-blue border-b-2 border-facebook-blue' 
                    : 'text-facebook-text-gray hover:bg-gray-100'
                }`}
              >
                Posts
              </button>
              <button 
                onClick={() => setActiveTab('about')}
                className={`px-4 py-3 font-medium ${
                  activeTab === 'about' 
                    ? 'text-facebook-blue border-b-2 border-facebook-blue' 
                    : 'text-facebook-text-gray hover:bg-gray-100'
                }`}
              >
                About
              </button>
              <button 
                onClick={() => setActiveTab('friends')}
                className={`px-4 py-3 font-medium ${
                  activeTab === 'friends' 
                    ? 'text-facebook-blue border-b-2 border-facebook-blue' 
                    : 'text-facebook-text-gray hover:bg-gray-100'
                }`}
              >
                Friends
              </button>
              <button 
                onClick={() => setActiveTab('photos')}
                className={`px-4 py-3 font-medium ${
                  activeTab === 'photos' 
                    ? 'text-facebook-blue border-b-2 border-facebook-blue' 
                    : 'text-facebook-text-gray hover:bg-gray-100'
                }`}
              >
                Photos
              </button>
            </div>
          </div>
        </div>
        
        {/* Main Content */}
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Left Column */}
            <div className="w-full md:w-1/3 space-y-4">
              <div className="bg-white rounded-lg shadow p-4">
                <h2 className="text-xl font-bold mb-3">Intro</h2>
                <div className="space-y-3">
                  <button className="w-full py-2 bg-facebook-gray hover:bg-facebook-gray-dark rounded-lg font-medium">
                    Add Bio
                  </button>
                  
                  <div className="flex items-center space-x-2">
                    <MapPin className="w-5 h-5 text-facebook-text-gray" />
                    <span>Lives in <b>New York City</b></span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Briefcase className="w-5 h-5 text-facebook-text-gray" />
                    <span>Works at <b>Tech Company</b></span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <GraduationCap className="w-5 h-5 text-facebook-text-gray" />
                    <span>Studied at <b>University</b></span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Heart className="w-5 h-5 text-facebook-text-gray" />
                    <span><b>Single</b></span>
                  </div>
                  
                  <button className="w-full py-2 bg-facebook-gray hover:bg-facebook-gray-dark rounded-lg font-medium">
                    Edit Details
                  </button>
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow p-4">
                <div className="flex items-center justify-between mb-3">
                  <h2 className="text-xl font-bold">Photos</h2>
                  <a href="#" className="text-facebook-blue hover:underline">See All Photos</a>
                </div>
                <div className="grid grid-cols-3 gap-1">
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((item) => (
                    <div key={item} className="aspect-square">
                      <img
                        src={`https://via.placeholder.com/100/1877f2/ffffff?text=Photo${item}`}
                        alt={`Photo ${item}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow p-4">
                <div className="flex items-center justify-between mb-3">
                  <h2 className="text-xl font-bold">Friends</h2>
                  <a href="#" className="text-facebook-blue hover:underline">See All Friends</a>
                </div>
                <div className="text-facebook-text-gray mb-2">120 friends</div>
                <div className="grid grid-cols-3 gap-2">
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((item) => (
                    <div key={item} className="text-center">
                      <img
                        src={`https://via.placeholder.com/100/1877f2/ffffff?text=F${item}`}
                        alt={`Friend ${item}`}
                        className="w-full aspect-square object-cover rounded-lg mb-1"
                      />
                      <div className="text-sm font-medium truncate">Friend {item}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            {/* Right Column - Posts */}
            <div className="w-full md:w-2/3 space-y-4">
              {/* Create Post */}
              <div className="bg-white rounded-lg shadow p-4">
                <div className="flex space-x-3">
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-10 h-10 rounded-full"
                  />
                  <button
                    className="flex-1 text-left px-4 py-3 bg-facebook-gray rounded-full hover:bg-facebook-gray-dark transition-colors text-facebook-text-gray"
                  >
                    What's on your mind, {user.name.split(' ')[0]}?
                  </button>
                </div>
              </div>
              
              {/* Posts */}
              <div className="space-y-4">
                {userPosts.map(post => (
                  <Post 
                    key={post.id} 
                    post={post} 
                    onLike={handleLike}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}