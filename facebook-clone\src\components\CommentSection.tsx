'use client'

import { useState } from 'react'

interface Comment {
  id: number
  user: {
    name: string
    avatar: string
  }
  content: string
  timestamp: string
}

interface CommentSectionProps {
  postId: number
  comments: Comment[]
  currentUser: {
    name: string
    avatar: string
  }
  onAddComment: (postId: number, comment: string) => void
}

export default function CommentSection({ 
  postId, 
  comments, 
  currentUser, 
  onAddComment 
}: CommentSectionProps) {
  const [newComment, setNewComment] = useState('')
  const [showAllComments, setShowAllComments] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (newComment.trim()) {
      onAddComment(postId, newComment)
      setNewComment('')
    }
  }

  // Show only 2 comments if not expanded
  const displayedComments = showAllComments ? comments : comments.slice(0, 2)

  return (
    <div className="px-4 pb-3">
      {/* Comments list */}
      {comments.length > 0 && (
        <div className="mb-3">
          {comments.length > 2 && !showAllComments && (
            <button 
              onClick={() => setShowAllComments(true)}
              className="text-facebook-text-gray text-sm mb-2 hover:underline"
            >
              View all {comments.length} comments
            </button>
          )}
          
          <div className="space-y-2">
            {displayedComments.map((comment) => (
              <div key={comment.id} className="flex space-x-2">
                <img
                  src={comment.user.avatar}
                  alt={comment.user.name}
                  className="w-8 h-8 rounded-full"
                />
                <div className="flex-1">
                  <div className="bg-facebook-gray rounded-2xl px-3 py-2">
                    <div className="font-semibold text-sm text-facebook-text-dark">
                      {comment.user.name}
                    </div>
                    <div className="text-sm text-facebook-text-dark">
                      {comment.content}
                    </div>
                  </div>
                  <div className="flex space-x-3 mt-1 text-xs text-facebook-text-gray">
                    <button className="font-semibold hover:underline">Like</button>
                    <button className="font-semibold hover:underline">Reply</button>
                    <span>{comment.timestamp}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {showAllComments && comments.length > 2 && (
            <button 
              onClick={() => setShowAllComments(false)}
              className="text-facebook-text-gray text-sm mt-2 hover:underline"
            >
              Hide comments
            </button>
          )}
        </div>
      )}

      {/* Add comment form */}
      <form onSubmit={handleSubmit} className="flex items-center space-x-2">
        <img
          src={currentUser.avatar}
          alt={currentUser.name}
          className="w-8 h-8 rounded-full"
        />
        <div className="flex-1 relative">
          <input
            type="text"
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Write a comment..."
            className="w-full bg-facebook-gray rounded-full py-2 px-3 pr-10 text-sm focus:outline-none"
          />
          {newComment && (
            <button 
              type="submit"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-facebook-blue"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
              </svg>
            </button>
          )}
        </div>
      </form>
    </div>
  )
}